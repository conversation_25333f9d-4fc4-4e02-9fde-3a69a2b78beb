/**
 * 事项类型配置
 * 统一管理所有事项类型相关的映射关系
 */

// 事项类型基础配置
export interface TrackerTypeConfig {
  value: string // 后端值
  label: string // 显示名称
  color: string // 默认颜色
  identifier: string // 默认识别符
  tagType: string // Element Plus tag 类型
  icon: string // 图标名称
  workflowLabel?: string // 工作流标签
  order?: number // 排序
}

// 事项类型配置映射
export const TRACKER_TYPE_CONFIG: Record<string, TrackerTypeConfig> = {
  requirement: {
    value: 'requirement',
    label: '需求',
    color: '#409EFF',
    identifier: 'REQ',
    tagType: 'primary',
    icon: 'file-text-line',
    workflowLabel: '需求工作流',
    order: 1,
  },
  bug: {
    value: 'bug',
    label: '缺陷',
    color: '#F56C6C',
    identifier: 'BUG',
    tagType: 'danger',
    icon: 'bug-line',
    workflowLabel: '缺陷工作流',
    order: 2,
  },
  task: {
    value: 'task',
    label: '任务',
    color: '#67C23A',
    identifier: 'TASK',
    tagType: 'success',
    icon: 'task-line',
    workflowLabel: '任务工作流',
    order: 3,
  },
  product: {
    value: 'product',
    label: '产品',
    color: '#9C27B0',
    identifier: 'PRODUCT',
    tagType: 'primary',
    icon: 'task-line',
    workflowLabel: '产品工作流',
    order: 4,
  },
}

// 获取所有事项类型选项（用于下拉框）
export const getTrackerTypeOptions = () => {
  return Object.values(TRACKER_TYPE_CONFIG).map((config) => ({
    label: config.label,
    value: config.value,
  }))
}

// 获取事项类型显示名称
export const getTrackerTypeLabel = (trackerType: string): string => {
  return TRACKER_TYPE_CONFIG[trackerType]?.label || trackerType
}

// 获取事项类型默认颜色
export const getTrackerTypeColor = (trackerType: string): string => {
  return TRACKER_TYPE_CONFIG[trackerType]?.color || '#409EFF'
}

// 获取事项类型默认识别符
export const getTrackerTypeIdentifier = (
  trackerType: string,
  name?: string
): string => {
  const defaultIdentifier = TRACKER_TYPE_CONFIG[trackerType]?.identifier
  if (defaultIdentifier) {
    return defaultIdentifier
  }
  // 如果没有配置，则使用名称的前3个字符
  return name ? name.substring(0, 3).toUpperCase() : 'XXX'
}

// 获取事项类型标签类型（Element Plus）
export const getTrackerTypeTagType = (trackerType: string): string => {
  return TRACKER_TYPE_CONFIG[trackerType]?.tagType || 'info'
}

// 获取事项类型图标
export const getTrackerTypeIcon = (trackerType: string): string => {
  return TRACKER_TYPE_CONFIG[trackerType]?.icon || 'task-line'
}

// 获取完整的事项类型配置
export const getTrackerTypeConfig = (
  trackerType: string
): TrackerTypeConfig | null => {
  return TRACKER_TYPE_CONFIG[trackerType] || null
}

// 检查是否为有效的事项类型
export const isValidTrackerType = (trackerType: string): boolean => {
  return trackerType in TRACKER_TYPE_CONFIG
}

// 获取所有事项类型值
export const getAllTrackerTypes = (): string[] => {
  return Object.keys(TRACKER_TYPE_CONFIG)
}

// 获取所有事项类型标签
export const getAllTrackerTypeLabels = (): string[] => {
  return Object.values(TRACKER_TYPE_CONFIG).map((config) => config.label)
}

// 获取有序的事项类型列表
export const getOrderedTrackerTypes = (): TrackerTypeConfig[] => {
  return Object.values(TRACKER_TYPE_CONFIG).sort(
    (a, b) => (a.order || 999) - (b.order || 999)
  )
}

// 获取工作流标签映射
export const getTrackerTypeWorkflowLabels = (): Record<string, string> => {
  const labels: Record<string, string> = {}
  Object.entries(TRACKER_TYPE_CONFIG).forEach(([key, config]) => {
    labels[key] = config.workflowLabel || `${config.label}工作流`
  })
  return labels
}

// 获取事项类型工作流标签
export const getTrackerTypeWorkflowLabel = (trackerType: string): string => {
  const config = TRACKER_TYPE_CONFIG[trackerType]
  return config?.workflowLabel || `${config?.label || trackerType}工作流`
}
