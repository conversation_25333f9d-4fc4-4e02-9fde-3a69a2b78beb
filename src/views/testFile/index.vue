<template>
  <div class="test-file-main-container">
    <!-- 顶部标题 -->
    <div class="test-file-main-header">
      <h2>厂测&老化文件</h2>
    </div>

    <!-- Tab切换区域 -->
    <el-tabs v-model="activeTab" class="test-file-main-tabs">
      <!-- 文件管理Tab -->
      <el-tab-pane label="文件管理" name="fileManager">
        <FileManagerView />
      </el-tab-pane>

      <!-- 目录重排Tab -->
      <el-tab-pane label="目录重排" name="reorganize">
        <el-card class="test-file-main-reorganize-card" shadow="never">
          <template #header>
            <div class="test-file-main-card-header">
              <span>重排任务管理</span>
              <el-button type="primary" @click="handleCreateTask">
                创建任务
              </el-button>
            </div>
          </template>

          <el-table
            :data="taskList"
            stripe
            v-loading="taskLoading"
            style="height: calc(100vh - 310px)"
          >
            <el-table-column prop="id" label="任务ID" width="80" />
            <el-table-column prop="task_type" label="任务类型" width="100">
              <template #default="{ row }">
                <el-tag :type="row.task_type === 'manual' ? '' : 'info'">
                  {{ row.task_type === 'manual' ? '手动' : '定时' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="source_dir" label="源目录" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getTaskStatusType(row.status)">
                  {{ getTaskStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="processed_files"
              label="处理进度"
              width="120"
            >
              <template #default="{ row }">
                {{ row.processed_files }}/{{ row.total_files }}
              </template>
            </el-table-column>
            <el-table-column prop="started_at" label="开始时间" width="160" />
            <el-table-column prop="completed_at" label="完成时间" width="160" />
          </el-table>

          <!-- 任务列表分页 -->
          <div class="test-file-main-pagination">
            <el-pagination
              background
              v-model:current-page="taskPagination.page"
              v-model:page-size="taskPagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="taskPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleTaskSizeChange"
              @current-change="handleTaskPageChange"
            />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建任务对话框 -->
    <firefly-dialog v-model="showTaskDialog" title="创建重排任务" width="500px">
      <el-form :model="taskForm" label-width="100px">
        <!-- <el-form-item label="源目录">
          <el-input v-model="taskForm.source_dir" placeholder="/tmp/test" />
        </el-form-item>
        <el-form-item label="目标目录">
          <el-input v-model="taskForm.target_dir" placeholder="/tmp/test2" />
        </el-form-item> -->
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="taskForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showTaskDialog = false">取消</el-button>
        <el-button type="primary" @click="submitTask">确定</el-button>
      </template>
    </firefly-dialog>

    <!-- 添加产品对话框 -->
    <firefly-dialog
      v-model="showProductDialog"
      :title="productForm.id ? '编辑产品' : '添加产品'"
      width="500px"
    >
      <el-form :model="productForm" label-width="100px">
        <el-form-item label="产品名称">
          <el-input
            v-model="productForm.product_name"
            placeholder="请输入产品名称"
          />
        </el-form-item>
        <el-form-item label="产品代码">
          <el-input
            v-model="productForm.product_code"
            placeholder="请输入产品代码"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="productForm.description"
            type="textarea"
            rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showProductDialog = false">取消</el-button>
        <el-button type="primary" @click="submitProduct">确定</el-button>
      </template>
    </firefly-dialog>

    <!-- 文件内容查看对话框 -->
    <firefly-dialog
      v-model="showFileContentDialog"
      :title="currentFile ? `查看文件：${currentFile.filename}` : '文件内容'"
      width="70%"
    >
      <div
        class="test-file-content-viewer"
        v-loading="fileContentLoading"
        style="max-height: 80vh; overflow: auto"
      >
        <pre style="padding: 10px" v-if="fileContent">{{ fileContent }}</pre>
        <div v-else class="test-file-no-content">暂无内容或文件不可预览</div>
      </div>
      <template #footer>
        <el-button @click="showFileContentDialog = false">关闭</el-button>
        <el-button type="primary" @click="handleDownload(currentFile)">
          <el-icon><Download /></el-icon>
          下载文件
        </el-button>
      </template>
    </firefly-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, nextTick, inject } from 'vue'
  import * as echarts from 'echarts'
  import * as api from '@/api/testFile'
  import FileManagerView from './components/file-manager/FileManagerView.vue'
  import dayjs from 'dayjs'

  // 注入自定义消息组件
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Tab相关
  const activeTab = ref('fileManager')

  // 文件管理相关
  const queryForm = reactive({
    product: '',
    sn: '',
    test_type: '',
    dateRange: [],
  })

  const fileList = ref([])
  const treeData = ref([])
  const loading = ref(false)
  const treeRef = ref(null)
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
  })

  // a-tree相关
  const selectedKeys = ref([])
  const expandedKeys = ref([])
  const currentNode = ref(null)
  const breadcrumbItems = ref([{ id: 'root', label: '#', path: [] }])

  // localStorage键名
  const STORAGE_KEY = 'testFile_tree_expanded_keys'

  // 文件查看相关
  const showFileContentDialog = ref(false)
  const currentFile = ref(null)
  const fileContent = ref('')
  const fileContentLoading = ref(false)

  const today = dayjs().format('YYYY-MM-DD')

  // 目录重排相关
  const taskList = ref([])
  const taskLoading = ref(false)
  const showTaskDialog = ref(false)
  const taskForm = reactive({
    date_range: [today, today],
  })
  const taskPagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
  })

  // 统计分析相关
  const summaryStats = ref([
    { title: '文件总数', value: '0' },
    { title: '产品数量', value: '0' },
    { title: '设备数量', value: '0' },
    { title: '总大小', value: '0 GB' },
  ])
  const fileCountChart = ref(null)
  const productChart = ref(null)

  // 产品管理相关
  const products = ref([])
  const productList = ref([])
  const productLoading = ref(false)
  const showProductDialog = ref(false)
  const productForm = reactive({
    id: null,
    product_name: '',
    product_code: '',
    description: '',
  })

  // 文件管理方法
  const handleQuery = async () => {
    loading.value = true
    try {
      const params = {
        ...queryForm,
        page: pagination.page,
        page_size: pagination.pageSize,
      }

      if (queryForm.dateRange && queryForm.dateRange.length === 2) {
        params.start_date = queryForm.dateRange[0]
        params.end_date = queryForm.dateRange[1]
      }

      const { data } = await api.getFileList(params)
      fileList.value = data.list || []
      pagination.total = data.total || 0
    } catch (error) {
      $baseMessage(
        '查询失败: ' + error.message,
        'error',
        'vab-hey-message-error'
      )
    } finally {
      loading.value = false
    }
  }

  const handleReset = () => {
    queryForm.product = ''
    queryForm.sn = ''
    queryForm.test_type = ''
    queryForm.dateRange = []
    pagination.page = 1
    handleQuery()
  }

  // a-tree 加载数据
  const loadNode = async (treeNode) => {
    if (!treeNode.dataRef.children) {
      try {
        const params = {
          node: treeNode.dataRef.id,
          depth: 1,
          for_tree: true, // 树形组件不需要分页
        }

        // 根据节点类型添加过滤条件
        const nodeType = treeNode.dataRef.type
        if (nodeType === 'product' && queryForm.sn) {
          params.filter = { sn: queryForm.sn }
        } else if (nodeType === 'sn' && queryForm.dateRange?.length === 2) {
          params.filter = {
            start_date: queryForm.dateRange[0],
            end_date: queryForm.dateRange[1],
          }
        }

        const { data } = await api.getTreeData(params)
        treeNode.dataRef.children = data.list || []
        treeData.value = [...treeData.value]
      } catch (error) {
        console.error('加载节点失败', error)
      }
    }
  }

  // 初始加载树数据
  const initTreeData = async () => {
    try {
      const params = {
        node: '',
        depth: 1,
        for_tree: true, // 树形组件不需要分页
      }

      // 添加过滤条件（如果有）
      if (queryForm.product) {
        params.filter = { product: queryForm.product }
        params.op = { product: 'LIKE' }
      }

      const { data } = await api.getTreeData(params)
      treeData.value = data.list || []

      // 从 localStorage恢复展开状态
      const savedExpandedKeys = localStorage.getItem(STORAGE_KEY)
      if (savedExpandedKeys) {
        try {
          expandedKeys.value = JSON.parse(savedExpandedKeys)
        } catch (e) {
          expandedKeys.value = []
        }
      }
    } catch (error) {
      console.error('加载树数据失败', error)
    }
  }

  // 树节点选择事件
  const handleNodeSelect = (keys, info) => {
    if (keys.length === 0) return

    selectedKeys.value = keys
    currentNode.value = info.node.dataRef

    console.log('info:', info)
    // 更新面包屑
    updateBreadcrumb(info.node)

    // 更新查询条件
    if (info.node.dataRef.type === 'product') {
      queryForm.product = info.node.dataRef.label
      queryForm.sn = ''
    } else if (info.node.dataRef.type === 'sn') {
      queryForm.sn = info.node.dataRef.label
      // 获取父节点（产品）
      const parent = findNodeParent(treeData.value, info.node.dataRef.id)
      if (parent) {
        queryForm.product = parent.label
      }
    }
    handleQuery()
  }

  // 树节点展开事件
  const handleExpand = (keys) => {
    expandedKeys.value = keys
    // 保存到localStorage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(keys))
  }

  // 更新面包屑
  const updateBreadcrumb = (node) => {
    const path = []
    let currentNode = node

    while (currentNode) {
      path.unshift({
        id: currentNode.dataRef.id,
        label: currentNode.dataRef.label,
        type: currentNode.dataRef.type,
        data: currentNode.dataRef,
      })
      currentNode = currentNode.parent
    }

    // 添加#
    path.unshift({ id: 'root', label: '#', path: [] })
    breadcrumbItems.value = path
  }

  // 面包屑点击事件
  const handleBreadcrumbClick = (item) => {
    if (item.id === 'root') {
      // 点击#，清空选择
      selectedKeys.value = []
      queryForm.product = ''
      queryForm.sn = ''
      breadcrumbItems.value = [{ id: 'root', label: '#', path: [] }]
    } else {
      // 选中对应节点
      selectedKeys.value = [item.id]

      // 展开路径上的所有节点
      const pathKeys = breadcrumbItems.value
        .slice(1, breadcrumbItems.value.indexOf(item) + 1)
        .map((i) => i.id)
      expandedKeys.value = [...new Set([...expandedKeys.value, ...pathKeys])]

      // 更新查询条件
      if (item.type === 'product') {
        queryForm.product = item.label
        queryForm.sn = ''
      } else if (item.type === 'sn') {
        queryForm.sn = item.label
      }
    }
    handleQuery()
  }

  // 查找节点的父节点
  const findNodeParent = (nodes, targetId, parent = null) => {
    for (const node of nodes) {
      if (node.id === targetId) {
        return parent
      }
      if (node.children && node.children.length > 0) {
        const found = findNodeParent(node.children, targetId, node)
        if (found) return found
      }
    }
    return null
  }

  // 查看文件内容
  const handleViewFile = async (file) => {
    currentFile.value = file
    showFileContentDialog.value = true
    fileContentLoading.value = true
    fileContent.value = ''

    try {
      const { data } = await api.getFileContent(file.id)

      // 处理不同类型的文件内容
      if (data.type === 'binary') {
        // 二进制文件，不能预览
        fileContent.value = ''
        const fileInfo = data.file_info
        const sizeText = api.formatFileSize(fileInfo.size)
        fileContent.value = `该文件为二进制文件（.${
          fileInfo.extension
        }），不支持预览\n\n文件信息：\n- 文件名：${
          fileInfo.name
        }\n- 文件大小：${sizeText}\n- 创建时间：${
          fileInfo.created_at || '未知'
        }\n\n请点击下方的“下载文件”按钮下载文件到本地查看。`
      } else if (data.type === 'text') {
        // 文本文件，可以预览
        if (data.content) {
          fileContent.value = data.content
        } else {
          fileContent.value = '文件内容为空'
        }
      } else {
        // 其他情况
        fileContent.value = data.message || '无法预览文件内容'
      }
    } catch (error) {
      // 处理错误情况
      if (error.response && error.response.data) {
        const errData = error.response.data
        if (errData.code === 3003) {
          fileContent.value =
            '文件太大，无法预览（限制5MB以内）\n\n请点击下方的“下载文件”按钮下载到本地查看。'
        } else {
          fileContent.value = errData.message || '加载文件内容失败'
        }
      } else {
        fileContent.value = '加载文件内容失败：' + error.message
      }
    } finally {
      fileContentLoading.value = false
    }
  }

  const handleSizeChange = (val) => {
    pagination.pageSize = val
    pagination.page = 1
    handleQuery()
  }

  const handleCurrentChange = (val) => {
    pagination.page = val
    handleQuery()
  }

  const handleDownload = async (file) => {
    if (!file) return

    try {
      const response = await api.downloadFile(file.id)
      const url = window.URL.createObjectURL(new Blob([response]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', file.filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      $baseMessage('下载成功', 'success', 'vab-hey-message-success')
    } catch (error) {
      $baseMessage(
        '下载失败: ' + error.message,
        'error',
        'vab-hey-message-error'
      )
    }
  }

  const handleDelete = (file) => {
    $baseConfirm(`确认删除文件 ${file.filename}？`, null, async () => {
      try {
        await api.deleteFile(file.id)
        $baseMessage('删除成功', 'success', 'vab-hey-message-success')
        handleQuery()
      } catch (error) {
        $baseMessage(
          '删除失败: ' + error.message,
          'error',
          'vab-hey-message-error'
        )
      }
    })
  }

  // 目录重排方法
  const handleCreateTask = () => {
    showTaskDialog.value = true
  }

  const submitTask = async () => {
    try {
      await api.createReorganizeTask(taskForm)
      $baseMessage('任务创建成功', 'success', 'vab-hey-message-success')
      showTaskDialog.value = false
      loadTaskList()
    } catch (error) {
      $baseMessage(
        '创建失败: ' + error.message,
        'error',
        'vab-hey-message-error'
      )
    }
  }

  const loadTaskList = async () => {
    taskLoading.value = true
    try {
      const params = {
        page: taskPagination.page,
        page_size: taskPagination.pageSize,
      }
      const { data } = await api.getTaskList(params)
      taskList.value = data.list || []
      taskPagination.total = data.total || 0
    } catch (error) {
      console.error('加载任务列表失败', error)
      $baseMessage(
        '加载任务列表失败: ' + error.message,
        'error',
        'vab-hey-message-error'
      )
    } finally {
      taskLoading.value = false
    }
  }

  // 任务列表分页处理
  const handleTaskPageChange = (page) => {
    taskPagination.page = page
    loadTaskList()
  }

  const handleTaskSizeChange = (size) => {
    taskPagination.pageSize = size
    taskPagination.page = 1
    loadTaskList()
  }

  const getTaskStatusType = (status) => {
    const map = { 0: 'info', 1: 'warning', 2: 'success', 3: 'danger' }
    return map[status] || 'info'
  }

  const getTaskStatusText = (status) => {
    const map = { 0: '待处理', 1: '处理中', 2: '完成', 3: '失败' }
    return map[status] || '未知'
  }

  // 统计分析方法
  const loadStatistics = async () => {
    try {
      const { data } = await api.getStatistics()
      summaryStats.value = [
        { title: '文件总数', value: data.total_files?.toLocaleString() || '0' },
        { title: '产品数量', value: data.total_products || '0' },
        { title: '设备数量', value: data.total_sns || '0' },
        { title: '总大小', value: api.formatFileSize(data.total_size) },
      ]

      // 初始化图表
      await nextTick()
      initCharts()
    } catch (error) {
      console.error('加载统计数据失败', error)
    }
  }

  const initCharts = () => {
    // 文件数量趋势图
    if (fileCountChart.value) {
      const chart = echarts.init(fileCountChart.value)
      chart.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130],
            type: 'line',
            smooth: true,
          },
        ],
      })
    }

    // 产品分布饼图
    if (productChart.value) {
      const chart = echarts.init(productChart.value)
      chart.setOption({
        tooltip: { trigger: 'item' },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: [
              { value: 335, name: 'AIO-3576-JD4' },
              { value: 310, name: 'AIO-3568-JD8' },
              { value: 234, name: 'ROC-3588S-PC' },
            ],
          },
        ],
      })
    }
  }

  // 产品管理方法
  const loadProducts = async () => {
    productLoading.value = true
    try {
      const { data } = await api.getProducts()
      products.value = data.list || []
      productList.value = data.list || []
    } catch (error) {
      console.error('加载产品列表失败', error)
    } finally {
      productLoading.value = false
    }
  }

  const handleAddProduct = () => {
    productForm.id = null
    productForm.product_name = ''
    productForm.product_code = ''
    productForm.description = ''
    showProductDialog.value = true
  }

  const handleEditProduct = (row) => {
    productForm.id = row.id
    productForm.product_name = row.product_name
    productForm.product_code = row.product_code
    productForm.description = row.description
    showProductDialog.value = true
  }

  const submitProduct = async () => {
    try {
      await api.createProduct(productForm)
      $baseMessage(
        productForm.id ? '编辑成功' : '添加成功',
        'success',
        'vab-hey-message-success'
      )
      showProductDialog.value = false
      loadProducts()
    } catch (error) {
      $baseMessage(
        '操作失败: ' + error.message,
        'error',
        'vab-hey-message-error'
      )
    }
  }

  const handleDeleteProduct = (row) => {
    $baseConfirm(`确认删除产品 ${row.product_name}？`, null, async () => {
      try {
        await api.deleteProduct(row.id)
        $baseMessage('删除成功', 'success', 'vab-hey-message-success')
        loadProducts()
      } catch (error) {
        $baseMessage(
          '删除失败: ' + error.message,
          'error',
          'vab-hey-message-error'
        )
      }
    })
  }

  // 工具函数
  const formatFileSize = api.formatFileSize

  const getStatusType = (status) => {
    const statusMap = {
      1: 'info',
      2: 'success',
      3: 'danger',
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status) => {
    const statusMap = {
      1: '已同步',
      2: '已重排',
      3: '已删除',
    }
    return statusMap[status] || '未知'
  }

  // 生命周期
  onMounted(async () => {
    loadProducts()
    initTreeData()
    handleQuery()
    loadTaskList()
    loadStatistics()
  })
</script>

<style scoped lang="scss">
  .test-file-main-container {
    padding: 20px;
    height: 100vh;

    .test-file-main-header {
      margin-bottom: 20px;

      h2 {
        margin: 0;
        color: #333;
      }
    }

    .test-file-main-tabs {
      height: calc(100vh - 100px);

      .test-file-main-query-form {
        width: 100%;
      }

      .test-file-main-content {
        min-height: calc(100vh - 240px);
      }

      .test-file-tree-card {
        height: calc(100vh - 172px);

        :deep(.el-card__body) {
          overflow-y: auto;
          height: calc(100vh - 228px);
        }

        // a-tree样式
        :deep(.ant-tree) {
          font-family: inherit;
          background: transparent;

          .ant-tree-switcher {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .ant-tree-node-content-wrapper {
            transition: all 0.3s;

            &:hover {
              background-color: #f5f7fa;
            }
          }

          .ant-tree-node-selected {
            .ant-tree-node-content-wrapper {
              background-color: #ecf5ff !important;
            }
          }
        }

        .test-file-custom-switcher-icon {
          transition: transform 0.3s;
        }
      }

      .test-file-tree-node {
        display: flex;
        align-items: center;
        font-size: 14px;

        .el-icon {
          margin-right: 5px;
        }

        .test-file-tree-count {
          margin-left: 5px;
          color: #909399;
          font-size: 12px;
        }
      }

      // 文件名链接样式
      .test-file-filename-link {
        color: #409eff;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: #66b1ff;
          text-decoration: underline;
        }
      }

      // 更多操作图标样式
      .test-file-more-icon {
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      // 文件内容查看器样式
      .test-file-content-viewer {
        min-height: 300px;
        max-height: 60vh;
        overflow-y: auto;

        pre {
          margin: 0;
          padding: 15px;
          background-color: #f5f7fa;
          border-radius: 4px;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 14px;
          line-height: 1.5;
          white-space: pre-wrap;
          word-wrap: break-word;
        }

        .test-file-no-content {
          padding: 50px 0;
          text-align: center;
          color: #909399;
          font-size: 14px;
        }
      }

      .test-file-main-reorganize-card,
      .test-file-main-stats {
        margin-top: 20px;
      }

      .test-file-main-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .test-file-main-stat-item {
        text-align: center;
        padding: 20px;

        .test-file-main-stat-value {
          font-size: 28px;
          font-weight: bold;
          color: #409eff;
          margin-bottom: 10px;
        }

        .test-file-main-stat-title {
          font-size: 14px;
          color: #666;
        }
      }

      .test-file-main-charts {
        margin-top: 20px;
      }

      .test-file-main-pagination {
        margin-top: 20px;
        display: flex;
        justify-content: flex-start;
      }
    }
  }
</style>
