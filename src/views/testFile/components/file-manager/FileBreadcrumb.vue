<template>
  <div class="file-breadcrumb-container">
    <!-- 导航按钮 -->
    <div class="breadcrumb-navigation">
      <el-button
        text
        :disabled="!canGoBack"
        size="small"
        :icon="Back"
        @click="handleNavigation('back')"
        title="后退"
      />
      <el-button
        text
        :disabled="!canGoForward"
        size="small"
        :icon="Right"
        @click="handleNavigation('forward')"
        title="前进"
      />
    </div>

    <!-- 面包屑路径 -->
    <div class="breadcrumb-path">
      <a-breadcrumb>
        <a-breadcrumb-item
          v-for="(item, index) in items"
          :key="item.id || index"
        >
          /
          <a
            v-if="index < items.length - 1"
            @click="emit('click', item)"
            class="file-breadcrumb-link"
          >
            {{ item.label }}
          </a>
          <span v-else>{{ item.label }}</span>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>
  </div>
</template>

<script setup>
  import { Back, Right } from '@element-plus/icons-vue'

  const props = defineProps({
    items: {
      type: Array,
      default: () => [],
    },
    canGoBack: {
      type: Boolean,
      default: false,
    },
    canGoForward: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['click', 'navigate'])

  // 处理导航操作
  const handleNavigation = (action) => {
    emit('navigate', action)
  }
</script>

<style scoped lang="scss">
  .file-breadcrumb-container {
    padding: 6px 0 12px 0;
    margin-bottom: 10px;
    border-bottom: 1px solid #dcdfe6;
    margin-top: -6px;
    display: flex;
    align-items: center;
    gap: 12px;

    .breadcrumb-navigation {
      display: flex;
      flex-shrink: 0;

      .el-button {
        width: 28px;
        height: 16px;
        padding: 0;

        &:disabled {
          opacity: 0.5;
        }
      }
    }

    .breadcrumb-path {
      flex: 1;
      min-width: 0;
    }

    :deep(.ant-breadcrumb) {
      font-size: 14px;

      .ant-breadcrumb-link {
        color: #606266;
      }

      .ant-breadcrumb-separator {
        margin: 0 8px;
        color: #c0c4cc;
      }
    }

    .file-breadcrumb-link {
      color: #409eff;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #66b1ff;
      }
    }
  }
</style>
