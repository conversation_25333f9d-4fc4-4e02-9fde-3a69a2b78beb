<template>
  <div class="entry-table-container">
    <el-table
      :data="data"
      v-loading="loading"
      stripe
      style="width: 100%; height: calc(-297px + 100vh)"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="名称" min-width="200">
        <template #default="{ row }">
          <span
            :class="
              row.type === 'folder'
                ? 'entry-table-folder-name'
                : 'entry-table-file-name'
            "
            @click="handleNameClick(row)"
          >
            <vab-icon
              v-if="row.type === 'folder'"
              :icon="'icon-dir'"
              is-custom-svg
              :style="{
                width: 16 + 'px',
                height: 16 + 'px',
                marginRight: '6px',
              }"
            />
            <vab-icon
              v-else
              :icon="'text_content'"
              is-custom-svg
              :style="{
                width: 20 + 'px',
                height: 20 + 'px',
                color: '#99999980',
                marginRight: '4px',
                marginLeft: '-2px',
              }"
            />
            {{ row.name }}
          </span>
        </template>
      </el-table-column>

      <!-- 文件夹特有列 -->
      <el-table-column
        v-if="hasFolder"
        prop="children_count"
        label="子项数量"
        width="100"
      >
        <template #default="{ row }">
          <span v-if="row.type === 'folder'">
            {{ row.children_count || 0 }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- 文件特有列 -->
      <el-table-column
        v-if="hasFile"
        prop="product"
        label="产品"
        min-width="150"
      />
      <el-table-column v-if="hasFile" prop="sn" label="序列号" width="160" />
      <el-table-column
        v-if="hasFile"
        prop="test_type"
        label="测试类型"
        width="100"
      >
        <template #default="{ row }">
          <template v-if="row.type === 'file'">
            <el-tag v-if="row.test_type === 'AgingTest'" type="primary">
              老化测试
            </el-tag>
            <el-tag v-else-if="row.test_type === 'FactoryTest'" type="success">
              厂测
            </el-tag>
            <span v-else>-</span>
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="hasFile"
        prop="file_size"
        label="文件大小"
        width="100"
      >
        <template #default="{ row }">
          <span v-if="row.type === 'file'">
            {{ formatFileSize(row.file_size) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="hasFile"
        prop="created_at"
        label="创建时间"
        width="160"
      >
        <template #default="{ row }">
          {{ row.file_mtime || '-' }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="80" fixed="right" v-if="hasFile">
        <template #default="{ row }">
          <!-- 文件夹操作 -->
          <el-button
            v-if="row.type === 'folder'"
            type="primary"
            size="small"
            @click="handleFolderClick(row)"
          >
            进入
          </el-button>

          <!-- 文件操作 -->
          <el-dropdown v-else trigger="click">
            <span class="entry-table-more-icon">
              <vab-icon
                icon="more"
                is-custom-svg
                :style="{
                  width: 16 + 'px',
                  height: 16 + 'px',
                }"
              />
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleFileClick(row)">
                  <el-icon><View /></el-icon>
                  查看
                </el-dropdown-item>
                <el-dropdown-item @click="$emit('file-download', row)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-dropdown-item>
                <el-dropdown-item @click="$emit('file-delete', row)" divided>
                  <el-icon color="#ff4d4f"><Delete /></el-icon>
                  <span style="color: #ff4d4f">删除</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="pagination.total > 0"
      background
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="pagination.total"
      :page-sizes="[20, 50, 100, 200]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="entry-table-pagination"
    />
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { View, Download, Delete } from '@element-plus/icons-vue'
  import * as api from '@/api/testFile'

  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    pagination: {
      type: Object,
      default: () => ({ page: 1, pageSize: 20, total: 0 }),
    },
  })

  const emit = defineEmits([
    'folder-click',
    'file-click',
    'file-download',
    'file-delete',
    'page-change',
    'size-change',
  ])

  // 判断是否包含文件夹或文件
  const hasFolder = computed(() =>
    props.data.some((item) => item.type === 'folder')
  )
  const hasFile = computed(() =>
    props.data.some((item) => item.type === 'file')
  )

  // 分页数据
  const currentPage = ref(props.pagination.page)
  const pageSize = ref(props.pagination.pageSize)

  watch(
    () => props.pagination.page,
    (newVal) => {
      currentPage.value = newVal
    }
  )

  watch(
    () => props.pagination.pageSize,
    (newVal) => {
      pageSize.value = newVal
    }
  )

  // 处理名称点击
  const handleNameClick = (row) => {
    if (row.type === 'folder') {
      emit('folder-click', row)
    } else {
      emit('file-click', row)
    }
  }

  // 处理文件夹点击
  const handleFolderClick = (folder) => {
    emit('folder-click', folder)
  }

  // 处理文件点击
  const handleFileClick = (file) => {
    emit('file-click', file)
  }

  // 处理分页
  const handleCurrentChange = (page) => {
    emit('page-change', page)
  }

  const handleSizeChange = (size) => {
    emit('size-change', size)
  }

  // 格式化文件大小
  const formatFileSize = api.formatFileSize
</script>

<style scoped lang="scss">
  .entry-table-container {
    width: 100%;

    .entry-table-folder-name,
    .entry-table-file-name {
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      transition: color 0.3s;

      &:hover {
        color: #409eff;
      }
    }

    .entry-table-file-name {
      color: #409eff;

      &:hover {
        color: #66b1ff;
        text-decoration: underline;
      }
    }

    .entry-table-more-icon {
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }
    }

    .entry-table-pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-start;
    }
  }
</style>
