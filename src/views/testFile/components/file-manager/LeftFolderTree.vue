<template>
  <a-tree
    v-if="treeData.length > 0"
    ref="treeRef"
    :tree-data="treeData"
    :field-names="{ key: 'id', title: 'label', children: 'children' }"
    :selected-keys="selectedKeys"
    :expanded-keys="expandedKeys"
    :block-node="true"
    @select="handleSelect"
    @expand="handleExpand"
    class="left-folder-tree"
  >
    <template #switcherIcon="{ expanded }">
      <el-icon
        class="left-folder-tree-switcher"
        :style="{
          transform: expanded ? 'rotate(90deg)' : 'rotate(0deg)',
          color: '#666666',
        }"
      >
        <ArrowRight />
      </el-icon>
    </template>
    <template #title="scope">
      <span
        class="left-folder-tree-node"
        @dblclick="handleNodeDoubleClick(scope)"
      >
        <vab-icon
          :icon="'icon-dir'"
          is-custom-svg
          :style="{
            width: 16 + 'px',
            height: 16 + 'px',
            marginRight: '6px',
          }"
        />
        <span>{{ scope.label }}</span>
        <span class="left-folder-tree-count">({{ scope.file_count }})</span>
      </span>
    </template>
  </a-tree>
  <div v-else-if="loading" class="left-folder-tree-loading">
    <el-icon class="is-loading"><Loading /></el-icon>
    <span>加载目录结构中...</span>
  </div>
  <div v-else class="left-folder-tree-empty">暂无目录数据</div>
</template>

<script setup>
  import { ref } from 'vue'
  import { ArrowRight, Loading } from '@element-plus/icons-vue'

  const props = defineProps({
    treeData: {
      type: Array,
      default: () => [],
    },
    selectedKeys: {
      type: Array,
      default: () => [],
    },
    expandedKeys: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['select', 'expand'])

  const treeRef = ref(null)

  // 处理节点选择
  const handleSelect = (keys, info) => {
    emit('select', keys, info)
  }

  // 处理节点展开
  const handleExpand = (keys, info) => {
    emit('expand', keys, info)
  }

  // 递归计算子节点数量
  const getChildrenCount = (node) => {
    if (!node.children || !Array.isArray(node.children)) {
      return 0
    }

    let count = 0
    const countRecursive = (children) => {
      children.forEach((child) => {
        if (child.type === 'date') {
          count++ // 只计算最终的日期节点
        } else if (child.children && Array.isArray(child.children)) {
          countRecursive(child.children)
        }
      })
    }

    countRecursive(node.children)
    return count
  }

  // 处理节点双击事件
  const handleNodeDoubleClick = (node) => {
    // 只有有子节点的节点才可以展开/收起
    if (!node.children || node.children.length === 0) {
      return
    }

    const nodeId = node.id
    const currentExpanded = props.expandedKeys.includes(nodeId)
    let newExpandedKeys = [...props.expandedKeys]

    if (currentExpanded) {
      // 当前节点已展开，执行收起（移除该节点及其所有子节点）
      const removeNodeAndChildren = (nodeToRemove) => {
        newExpandedKeys = newExpandedKeys.filter(
          (key) => key !== nodeToRemove.id
        )
        if (nodeToRemove.children) {
          nodeToRemove.children.forEach((child) => {
            removeNodeAndChildren(child)
          })
        }
      }
      removeNodeAndChildren(node)
    } else {
      // 当前节点未展开，执行展开
      newExpandedKeys.push(nodeId)
    }

    emit('expand', newExpandedKeys)
  }
</script>

<style scoped lang="scss">
  .left-folder-tree {
    font-family: inherit;
    background: transparent;

    :deep(.ant-tree-switcher) {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    :deep(.ant-tree-node-content-wrapper) {
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }
    }

    :deep(.ant-tree-node-selected) {
      .ant-tree-node-content-wrapper {
        background-color: #ecf5ff !important;
      }
    }

    &-switcher {
      transition: transform 0.3s;
    }

    &-node {
      display: flex;
      align-items: center;
      font-size: 14px;
    }

    &-count {
      margin-left: 5px;
      color: #909399;
      font-size: 12px;
    }
  }

  .left-folder-tree-empty {
    padding: 20px;
    text-align: center;
    color: #909399;
    font-size: 14px;
  }

  .left-folder-tree-loading {
    padding: 20px;
    text-align: center;
    color: #409eff;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .el-icon {
      font-size: 16px;
    }
  }
</style>
