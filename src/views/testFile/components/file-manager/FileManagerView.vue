<template>
  <div class="file-manager-view-container">
    <!-- 查询表单 -->
    <vab-query-form>
      <vab-query-form-left-panel :span="18">
        <el-form
          :inline="true"
          :model="queryForm"
          class="file-manager-view-query-form"
        >
          <el-form-item>
            <el-select
              v-model="queryForm.product"
              placeholder="请选择产品"
              clearable
              filterable
              style="width: 200px"
              @change="handleQuery"
            >
              <el-option
                v-for="item in filteredProducts"
                :key="item.id"
                :label="item.product_name"
                :value="item.product_name"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="queryForm.sn"
              placeholder="请输入sn号"
              clearable
              @keyup.enter="handleQuery"
              @input="handleDebounceQuery"
            />
          </el-form-item>

          <!-- 更多筛选 -->
          <el-form-item>
            <el-popover
              placement="bottom-start"
              :width="600"
              trigger="manual"
              v-model:visible="showMoreFilters"
              popper-class="file-manager-view-filter-popover el-form-in-popover"
            >
              <template #reference>
                <el-button @click="toggleMoreFilters">
                  <vab-icon
                    icon="filter"
                    is-custom-svg
                    style="width: 24px; height: 24px"
                  />
                  更多筛选
                </el-button>
              </template>

              <div class="file-manager-view-more-filters">
                <el-form :model="queryForm" label-width="80px">
                  <el-form-item label="创建日期" style="width: 400px">
                    <el-date-picker
                      v-model="queryForm.dateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      width="200px"
                      :teleported="false"
                    />
                  </el-form-item>
                  <el-form-item label="文件类型">
                    <el-select
                      v-model="queryForm.fileType"
                      placeholder="请选择文件类型"
                      clearable
                      @change="handleFileTypeChangeInPopover"
                      style="width: 200px"
                      :teleported="false"
                    >
                      <el-option label="全部文件" value="" />
                      <el-option label="老化测试" value="aging_test" />
                      <el-option label="厂测" value="factory_test" />
                    </el-select>
                  </el-form-item>
                </el-form>

                <!-- 老化测试筛选条件 -->
                <div
                  v-if="queryForm.fileType === 'aging_test'"
                  class="file-manager-view-filter-section"
                >
                  <h4>老化测试筛选条件</h4>
                  <el-form :model="agingFilters" label-width="80px">
                    <el-row :gutter="20">
                      <el-col :span="24">
                        <el-form-item label="运行时间">
                          <div class="file-manager-view-range-input">
                            <el-input-number
                              v-model="agingFilters.runtime_hours_min"
                              :min="0"
                              :precision="1"
                              :step="1"
                              placeholder="最小值"
                              style="width: 140px"
                            />
                            <span>至</span>
                            <el-input-number
                              v-model="agingFilters.runtime_hours_max"
                              :min="0"
                              :precision="1"
                              :step="1"
                              placeholder="最大值"
                              style="width: 140px"
                            />
                            <span>小时</span>
                          </div>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>

                <!-- 厂测筛选条件 -->
                <div
                  v-if="queryForm.fileType === 'factory_test'"
                  class="file-manager-view-filter-section"
                >
                  <h4>厂测筛选条件</h4>
                  <el-form :model="factoryFilters" label-width="80px">
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="测试结果">
                          <el-select
                            v-model="factoryFilters.factory_test_result"
                            placeholder="请选择"
                            clearable
                            :teleported="false"
                          >
                            <el-option label="成功" value="成功" />
                            <el-option label="失败" value="失败" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="设备名">
                          <el-input
                            v-model="factoryFilters.device_name"
                            placeholder="设备名"
                            clearable
                            @keyup.enter="applyFiltersAndClose"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="CPUID">
                          <el-input
                            v-model="factoryFilters.cpuid"
                            placeholder="CPUID"
                            clearable
                            @keyup.enter="applyFiltersAndClose"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="厂测版本">
                          <el-input
                            v-model="factoryFilters.factory_test_version"
                            placeholder="厂测版本"
                            clearable
                            @keyup.enter="applyFiltersAndClose"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="固件版本">
                          <el-input
                            v-model="factoryFilters.firmware_version"
                            placeholder="固件版本"
                            clearable
                            @keyup.enter="applyFiltersAndClose"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="DDR">
                          <el-input
                            v-model="factoryFilters.ddr"
                            placeholder="DDR"
                            clearable
                            @keyup.enter="applyFiltersAndClose"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="Flash">
                          <el-input
                            v-model="factoryFilters.flash"
                            placeholder="Flash"
                            clearable
                            @keyup.enter="applyFiltersAndClose"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row>
                      <el-col :span="24">
                        <el-form-item label="成功项目">
                          <el-checkbox-group
                            v-model="factoryFilters.success_projects"
                          >
                            <el-checkbox label="MEMORY">MEMORY</el-checkbox>
                            <el-checkbox label="TIME">TIME</el-checkbox>
                            <el-checkbox label="HDMI_EDID">
                              HDMI_EDID
                            </el-checkbox>
                            <el-checkbox label="DEVICE_CHK">
                              DEVICE_CHK
                            </el-checkbox>
                            <el-checkbox label="SERIAL_PORT">
                              SERIAL_PORT
                            </el-checkbox>
                            <el-checkbox label="SPI_FLASH">
                              SPI_FLASH
                            </el-checkbox>
                            <el-checkbox label="PCIE">PCIE</el-checkbox>
                            <el-checkbox label="HW_VERSION">
                              HW_VERSION
                            </el-checkbox>
                            <el-checkbox label="USB">USB</el-checkbox>
                            <el-checkbox label="BLUETOOTH">
                              BLUETOOTH
                            </el-checkbox>
                            <el-checkbox label="LED">LED</el-checkbox>
                            <el-checkbox label="SIMCARD">SIMCARD</el-checkbox>
                            <el-checkbox label="SATA">SATA</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="24">
                        <el-form-item label="失败项目">
                          <el-checkbox-group
                            v-model="factoryFilters.failed_projects"
                          >
                            <el-checkbox label="MEMORY">MEMORY</el-checkbox>
                            <el-checkbox label="TIME">TIME</el-checkbox>
                            <el-checkbox label="HDMI_EDID">
                              HDMI_EDID
                            </el-checkbox>
                            <el-checkbox label="DEVICE_CHK">
                              DEVICE_CHK
                            </el-checkbox>
                            <el-checkbox label="SERIAL_PORT">
                              SERIAL_PORT
                            </el-checkbox>
                            <el-checkbox label="SPI_FLASH">
                              SPI_FLASH
                            </el-checkbox>
                            <el-checkbox label="PCIE">PCIE</el-checkbox>
                            <el-checkbox label="HW_VERSION">
                              HW_VERSION
                            </el-checkbox>
                            <el-checkbox label="USB">USB</el-checkbox>
                            <el-checkbox label="BLUETOOTH">
                              BLUETOOTH
                            </el-checkbox>
                            <el-checkbox label="LED">LED</el-checkbox>
                            <el-checkbox label="SIMCARD">SIMCARD</el-checkbox>
                            <el-checkbox label="SATA">SATA</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>

                <!-- 操作按钮 -->
                <div class="file-manager-view-filter-actions">
                  <!-- <el-button @click="resetAllFilters">重置筛选</el-button> -->
                  <el-button type="primary" @click="applyFiltersAndClose">
                    应用筛选
                  </el-button>
                </div>
              </div>
            </el-popover>
          </el-form-item>

          <el-form-item>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="6">
        <!-- <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="handleReset">重置</el-button> -->
      </vab-query-form-right-panel>
    </vab-query-form>

    <div style="display: flex; gap: 20px; width: 100%">
      <!-- 左侧文件夹树 -->
      <div style="width: 300px">
        <el-card class="file-manager-view-tree-card" shadow="never">
          <template #header>
            <div
              style="
                display: flex;
                justify-content: space-between;
                align-items: center;
              "
            >
              <span>目录结构</span>
              <el-tooltip content="一键展开/收起" placement="top">
                <el-button
                  text
                  size="small"
                  style="width: 32px; padding: 0"
                  @click="changeExpand()"
                >
                  <vab-icon
                    style="margin-left: 3px"
                    icon="expand_up_down"
                    is-custom-svg
                  />
                </el-button>
              </el-tooltip>
            </div>
          </template>
          <LeftFolderTree
            :tree-data="treeData"
            :selected-keys="selectedKeys"
            :expanded-keys="expandedKeys"
            :loading="treeLoading"
            @select="handleTreeSelect"
            @expand="handleTreeExpand"
          />
        </el-card>
      </div>

      <!-- 右侧内容区 -->
      <div style="width: calc(100% - 300px)">
        <vab-card shadow="never" class="file-manager-view-content-card-right">
          <!-- 面包屑导航 -->
          <FileBreadcrumb
            :items="breadcrumbItems"
            :can-go-back="navigationState.canGoBack"
            :can-go-forward="navigationState.canGoForward"
            @click="handleBreadcrumbClick"
            @navigate="handleBreadcrumbNavigation"
          />

          <!-- 文件和文件夹表格 -->
          <EntryTable
            :data="entryList"
            :loading="loading"
            :pagination="pagination"
            @folder-click="handleFolderClick"
            @file-click="handleFileClick"
            @file-download="handleFileDownload"
            @file-delete="handleFileDelete"
            @page-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </vab-card>
      </div>
    </div>

    <!-- 文件预览对话框 -->
    <FilePreviewDialog
      v-model="showFilePreview"
      :file="currentFile"
      :content="fileContent"
      :loading="fileContentLoading"
      @download="handleFileDownload"
    />
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, inject, watch } from 'vue'
  import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
  import _ from 'lodash'
  import * as api from '@/api/testFile'
  import LeftFolderTree from './LeftFolderTree.vue'
  import FileBreadcrumb from './FileBreadcrumb.vue'
  import EntryTable from './EntryTable.vue'
  import FilePreviewDialog from './FilePreviewDialog.vue'

  // 注入消息组件
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // 产品列表
  const products = ref([])

  // 查询表单
  const queryForm = reactive({
    product: '',
    sn: '',
    fileType: '', // 新增文件类型
    dateRange: [],
  })

  // 更多筛选弹窗
  const showMoreFilters = ref(false)

  // 老化测试筛选条件
  const agingFilters = reactive({
    runtime_hours_min: null,
    runtime_hours_max: null,
  })

  // 厂测筛选条件
  const factoryFilters = reactive({
    factory_test_result: '', // 厂测结果
    device_name: '', // 设备名
    cpuid: '', // CPUID
    factory_test_version: '', // 厂测版本
    firmware_version: '', // 固件版本
    ddr: '', // DDR
    flash: '', // Flash
    success_projects: [], // 成功项目
    failed_projects: [], // 失败项目
    test_items: [], // 测试项（保留兼容）
  })

  // 当前路径
  const currentNodePath = ref([])

  // 导航历史管理
  const navigationHistory = ref([[]]) // 初始化为根目录
  const currentHistoryIndex = ref(0)
  const isNavigatingFromHistory = ref(false) // 标记是否从历史导航

  // 树形数据
  const treeData = ref([])
  const selectedKeys = ref([])
  const expandedKeys = ref([])
  const treeLoading = ref(false)

  // 表格数据
  const entryList = ref([])
  const loading = ref(false)
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
  })

  // 文件预览
  const showFilePreview = ref(false)
  const currentFile = ref(null)
  const fileContent = ref('')
  const fileContentLoading = ref(false)

  // localStorage键名
  const STORAGE_KEY = 'testFile_tree_expanded_keys'

  // 控制展开/收起状态
  const isExpanded = ref(false)

  // 计算面包屑
  const breadcrumbItems = computed(() => {
    const items = [{ id: 'root', label: '#', path: [] }]

    currentNodePath.value.forEach((segment, index) => {
      items.push({
        id: currentNodePath.value.slice(0, index + 1).join('/'),
        label: segment,
        type: index === 0 ? 'product' : index === 1 ? 'sn' : 'date',
        path: currentNodePath.value.slice(0, index + 1),
      })
    })

    return items
  })

  // 计算过滤后的产品列表（用于选择器的filterable功能）
  const filteredProducts = computed(() => {
    return products.value
  })

  // 监听路径变化，加载数据
  watch(currentNodePath, (newPath, oldPath) => {
    // 只有当路径确实发生变化时才添加到历史
    if (JSON.stringify(newPath) !== JSON.stringify(oldPath)) {
      addToHistory(newPath)
    }
    loadEntryList()
  })

  // 加载产品列表
  const loadProducts = async () => {
    try {
      const { data } = await api.getProducts()
      products.value = data.list || []
    } catch (error) {
      console.error('加载产品列表失败', error)
    }
  }

  // 初始化树数据（使用新的完整树结构API）
  const initTreeData = async (restoreExpandedState = true) => {
    treeLoading.value = true
    treeData.value = []

    try {
      const params = {}

      // 构建所有筛选条件
      const filters = buildAllFilters()
      if (filters.filter && Object.keys(filters.filter).length > 0) {
        params.filter = filters.filter
        params.op = filters.op
      }

      // 添加高级筛选条件（文件类型、老化测试、厂测）
      if (
        filters.advancedFilters &&
        Object.keys(filters.advancedFilters).length > 0
      ) {
        // 将高级筛选条件合并到params中
        Object.assign(params, filters.advancedFilters)
      }

      // 添加搜索条件
      if (queryForm.product || queryForm.sn) {
        params.search = (queryForm.product || '') + ' ' + (queryForm.sn || '')
        params.search = params.search.trim()
      }

      const { data } = await api.getFullTreeData(params)
      treeData.value = data.tree || []

      // 恢复展开状态
      if (restoreExpandedState) {
        const savedExpandedKeys = localStorage.getItem(STORAGE_KEY)
        if (savedExpandedKeys) {
          try {
            expandedKeys.value = JSON.parse(savedExpandedKeys)
          } catch (e) {
            expandedKeys.value = []
          }
        }
      }

      // 同步更新展开状态
      if (treeData.value.length > 0) {
        const allIds = getAllNodeIds(treeData.value)
        isExpanded.value = expandedKeys.value.length === allIds.length
      }
    } catch (error) {
      console.error('加载树数据失败', error)
      $baseMessage(
        '加载目录树失败: ' + error.message,
        'error',
        'vab-hey-message-error'
      )
    } finally {
      treeLoading.value = false
    }
  }

  // 加载表格数据
  const loadEntryList = async () => {
    loading.value = true
    entryList.value = []

    try {
      const pathLength = currentNodePath.value.length

      if (pathLength === 0) {
        // 根级别，显示产品文件夹
        const params = {
          node: '',
          page: pagination.page,
          limit: pagination.pageSize,
          for_tree: false, // 表格展示需要分页
        }

        // 构建所有筛选条件
        const filters = buildAllFilters()
        if (filters.filter && Object.keys(filters.filter).length > 0) {
          params.filter = filters.filter
          params.op = filters.op
        }

        // 添加高级筛选条件（文件类型、老化测试、厂测）
        if (
          filters.advancedFilters &&
          Object.keys(filters.advancedFilters).length > 0
        ) {
          Object.assign(params, filters.advancedFilters)
        }

        const { data } = await api.getTreeData(params)
        entryList.value = (data.list || []).map((item) => ({
          ...item,
          type: 'folder',
          name: item.label,
        }))
        pagination.total = data.total || 0
      } else if (pathLength === 1) {
        // 产品级别，显示SN文件夹
        const product = currentNodePath.value[0]
        const params = {
          node: product,
          page: pagination.page,
          limit: pagination.pageSize,
          for_tree: false,
        }

        // 构建所有筛选条件
        const filters = buildAllFilters()
        if (filters.filter && Object.keys(filters.filter).length > 0) {
          params.filter = filters.filter
          params.op = filters.op
        }

        // 添加高级筛选条件（文件类型、老化测试、厂测）
        if (
          filters.advancedFilters &&
          Object.keys(filters.advancedFilters).length > 0
        ) {
          Object.assign(params, filters.advancedFilters)
        }

        const { data } = await api.getTreeData(params)
        entryList.value = (data.list || []).map((item) => ({
          ...item,
          type: 'folder',
          name: item.label,
        }))
        pagination.total = data.total || 0
      } else if (pathLength === 2) {
        // SN级别，显示日期文件夹
        const [product, sn] = currentNodePath.value
        const params = {
          node: `${product}/${sn}`,
          page: pagination.page,
          limit: pagination.pageSize,
          for_tree: false,
          sort: 'test_datetime',
          order: 'DESC', // 日期默认倒序
        }

        // 构建所有筛选条件
        const filters = buildAllFilters()
        if (filters.filter && Object.keys(filters.filter).length > 0) {
          params.filter = filters.filter
          params.op = filters.op
        }

        // 添加高级筛选条件（文件类型、老化测试、厂测）
        if (
          filters.advancedFilters &&
          Object.keys(filters.advancedFilters).length > 0
        ) {
          Object.assign(params, filters.advancedFilters)
        }

        const { data } = await api.getTreeData(params)
        entryList.value = (data.list || []).map((item) => ({
          ...item,
          type: 'folder',
          name: item.label,
        }))
        pagination.total = data.total || 0
      } else if (pathLength === 3) {
        // 日期级别，显示文件
        const [product, sn, testDatetime] = currentNodePath.value
        const params = {
          product,
          sn,
          test_datetime: testDatetime,
          page: pagination.page,
          page_size: pagination.pageSize,
          file_type: queryForm.fileType, // 添加文件类型参数
        }

        // 添加老化测试筛选条件
        if (queryForm.fileType === 'aging_test' && hasAgingFilters()) {
          params.aging_filters = getActiveAgingFilters()
        }

        // 添加厂测筛选条件
        if (queryForm.fileType === 'factory_test' && hasFactoryFilters()) {
          params.factory_filters = getActiveFactoryFilters()
        }

        // 添加查询条件
        if (queryForm.dateRange && queryForm.dateRange.length === 2) {
          params.start_date = queryForm.dateRange[0]
          params.end_date = queryForm.dateRange[1]
        }

        const { data } = await api.getFileList(params)
        entryList.value = (data.list || []).map((item) => ({
          ...item,
          type: 'file',
          name: item.filename,
        }))
        pagination.total = data.total || 0
      }
    } catch (error) {
      $baseMessage(
        '加载数据失败: ' + error.message,
        'error',
        'vab-hey-message-error'
      )
    } finally {
      loading.value = false
    }
  }

  // 处理树选择
  const handleTreeSelect = (keys, info) => {
    if (keys.length === 0) return

    selectedKeys.value = keys
    const nodeId = keys[0]

    // 解析路径
    if (nodeId === 'root') {
      currentNodePath.value = []
    } else {
      currentNodePath.value = nodeId.split('/')
    }
  }

  // 处理树展开
  const handleTreeExpand = (keys) => {
    expandedKeys.value = keys
    localStorage.setItem(STORAGE_KEY, JSON.stringify(keys))

    // 更新展开状态
    if (keys.length === 0) {
      isExpanded.value = false
    } else if (treeData.value.length > 0) {
      const allIds = getAllNodeIds(treeData.value)
      isExpanded.value = keys.length === allIds.length
    }
  }

  // 处理面包屑点击
  const handleBreadcrumbClick = (item) => {
    if (item.id === 'root') {
      currentNodePath.value = []
      selectedKeys.value = []
    } else {
      currentNodePath.value = item.path
      selectedKeys.value = [item.id]

      // 展开路径上的所有节点
      const pathKeys = []
      for (let i = 1; i <= item.path.length; i++) {
        pathKeys.push(item.path.slice(0, i).join('/'))
      }
      expandedKeys.value = [...new Set([...expandedKeys.value, ...pathKeys])]
      localStorage.setItem(STORAGE_KEY, JSON.stringify(expandedKeys.value))
    }
  }

  // 处理面包屑导航事件
  const handleBreadcrumbNavigation = (action) => {
    if (action === 'back') {
      goBack()
    } else if (action === 'forward') {
      goForward()
    }
  }

  // 处理文件夹点击
  const handleFolderClick = (folder) => {
    const pathLength = currentNodePath.value.length

    if (pathLength === 0) {
      // 进入产品
      currentNodePath.value = [folder.label]
      selectedKeys.value = [folder.id]
    } else if (pathLength === 1) {
      // 进入SN
      currentNodePath.value = [...currentNodePath.value, folder.label]
      selectedKeys.value = [folder.id]
    } else if (pathLength === 2) {
      // 进入日期
      currentNodePath.value = [...currentNodePath.value, folder.label]
      selectedKeys.value = [folder.id]
    }

    // 展开对应节点
    expandedKeys.value = [
      ...new Set([...expandedKeys.value, ...selectedKeys.value]),
    ]
    localStorage.setItem(STORAGE_KEY, JSON.stringify(expandedKeys.value))
  }

  // 处理文件点击
  const handleFileClick = async (file) => {
    currentFile.value = file
    showFilePreview.value = true
    fileContentLoading.value = true
    fileContent.value = ''

    try {
      const { data } = await api.getFileContent(file.id)

      if (data.type === 'binary') {
        const fileInfo = data.file_info
        const sizeText = api.formatFileSize(fileInfo.size)
        fileContent.value = `该文件为二进制文件（.${
          fileInfo.extension
        }），不支持预览\n\n文件信息：\n- 文件名：${
          fileInfo.name
        }\n- 文件大小：${sizeText}\n- 创建时间：${
          fileInfo.created_at || '未知'
        }\n\n请点击下方的"下载文件"按钮下载文件到本地查看。`
      } else if (data.type === 'text') {
        fileContent.value = data.content || '文件内容为空'
      } else {
        fileContent.value = data.message || '无法预览文件内容'
      }
    } catch (error) {
      fileContent.value = '加载文件内容失败：' + error.message
    } finally {
      fileContentLoading.value = false
    }
  }

  // 处理文件下载
  const handleFileDownload = async (file) => {
    if (!file) return

    try {
      const response = await api.downloadFile(file.id)
      const url = window.URL.createObjectURL(new Blob([response]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', file.filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      $baseMessage('下载成功', 'success', 'vab-hey-message-success')
    } catch (error) {
      $baseMessage(
        '下载失败: ' + error.message,
        'error',
        'vab-hey-message-error'
      )
    }
  }

  // 处理文件删除
  const handleFileDelete = (file) => {
    $baseConfirm(`确认删除文件 ${file.filename}？`, null, async () => {
      try {
        await api.deleteFile(file.id)
        $baseMessage('删除成功', 'success', 'vab-hey-message-success')
        loadEntryList()
      } catch (error) {
        $baseMessage(
          '删除失败: ' + error.message,
          'error',
          'vab-hey-message-error'
        )
      }
    })
  }

  // 处理查询
  const handleQuery = _.debounce(() => {
    pagination.page = 1
    // 重置当前路径，回到根目录
    currentNodePath.value = []
    selectedKeys.value = []
    // 重新加载树形数据和表格数据
    initTreeData()
    loadEntryList()
  }, 200)

  const handleDebounceQuery = _.debounce(handleQuery, 850)

  // 处理重置
  const handleReset = () => {
    queryForm.product = ''
    queryForm.sn = ''
    queryForm.fileType = ''
    queryForm.dateRange = []
    // 重置高级筛选
    resetAllFilters()
    pagination.page = 1

    // 重置树形状态
    currentNodePath.value = []
    selectedKeys.value = []
    expandedKeys.value = []

    // 清除localStorage中的展开状态
    localStorage.removeItem(STORAGE_KEY)

    // 重新加载完整的树和表格数据（不恢复展开状态）
    initTreeData(false)
    loadEntryList()
  }

  // 处理分页
  const handlePageChange = (page) => {
    pagination.page = page
    loadEntryList()
  }

  const handleSizeChange = (size) => {
    pagination.pageSize = size
    pagination.page = 1
    loadEntryList()
  }

  // 文件类型改变
  const handleFileTypeChange = () => {
    handleQuery()
  }

  // 在弹窗内改变文件类型，不触发查询
  const handleFileTypeChangeInPopover = () => {
    // 仅更新筛选条件显示，不立即查询
  }

  // 切换更多筛选弹窗
  const toggleMoreFilters = () => {
    showMoreFilters.value = !showMoreFilters.value
  }

  // 检查是否有老化测试筛选条件
  const hasAgingFilters = () => {
    return Object.values(agingFilters).some((v) => v !== null && v !== '')
  }

  // 获取有效的老化测试筛选条件
  const getActiveAgingFilters = () => {
    const filters = {}
    Object.entries(agingFilters).forEach(([key, value]) => {
      if (value !== null && value !== '') {
        filters[key] = value
      }
    })
    return filters
  }

  // 检查是否有厂测筛选条件
  const hasFactoryFilters = () => {
    return (
      factoryFilters.factory_test_result ||
      factoryFilters.device_name ||
      factoryFilters.cpuid ||
      factoryFilters.factory_test_version ||
      factoryFilters.firmware_version ||
      factoryFilters.ddr ||
      factoryFilters.flash ||
      factoryFilters.success_projects.length > 0 ||
      factoryFilters.failed_projects.length > 0 ||
      factoryFilters.test_items.length > 0
    )
  }

  // 获取有效的厂测筛选条件
  const getActiveFactoryFilters = () => {
    const filters = {}
    if (factoryFilters.factory_test_result)
      filters.factory_test_result = factoryFilters.factory_test_result
    if (factoryFilters.device_name)
      filters.device_name = factoryFilters.device_name
    if (factoryFilters.cpuid) filters.cpuid = factoryFilters.cpuid
    if (factoryFilters.factory_test_version)
      filters.factory_test_version = factoryFilters.factory_test_version
    if (factoryFilters.firmware_version)
      filters.firmware_version = factoryFilters.firmware_version
    if (factoryFilters.ddr) filters.ddr = factoryFilters.ddr
    if (factoryFilters.flash) filters.flash = factoryFilters.flash
    if (factoryFilters.success_projects.length > 0)
      filters.success_projects = factoryFilters.success_projects
    if (factoryFilters.failed_projects.length > 0)
      filters.failed_projects = factoryFilters.failed_projects
    // 传递测试项数组（保留兼容）
    if (factoryFilters.test_items.length > 0)
      filters.test_items = factoryFilters.test_items
    return filters
  }

  // 重置老化测试筛选条件
  const resetAgingFilters = () => {
    Object.keys(agingFilters).forEach((key) => {
      agingFilters[key] = null
    })
  }

  // 重置厂测筛选条件
  const resetFactoryFilters = () => {
    factoryFilters.factory_test_result = ''
    factoryFilters.device_name = ''
    factoryFilters.cpuid = ''
    factoryFilters.factory_test_version = ''
    factoryFilters.firmware_version = ''
    factoryFilters.ddr = ''
    factoryFilters.flash = ''
    factoryFilters.success_projects = []
    factoryFilters.failed_projects = []
    factoryFilters.test_items = []
  }

  // 重置所有筛选条件
  const resetAllFilters = () => {
    queryForm.fileType = ''
    queryForm.dateRange = []
    resetAgingFilters()
    resetFactoryFilters()
  }

  // 获取活跃筛选条件数量
  const getActiveFiltersCount = () => {
    let count = 0
    if (queryForm.fileType) count++
    if (hasAgingFilters()) count++
    if (hasFactoryFilters()) count++
    return count
  }

  // 应用筛选并关闭弹窗
  const applyFiltersAndClose = () => {
    showMoreFilters.value = false
    handleQuery()
  }

  // 构建所有筛选条件
  const buildAllFilters = () => {
    const filter = {}
    const op = {}
    const advancedFilters = {}

    // 产品筛选
    if (queryForm.product) {
      filter.product = queryForm.product
      op.product = 'LIKE'
    }

    // SN筛选
    if (queryForm.sn) {
      filter.sn = queryForm.sn
      op.sn = 'LIKE'
    }

    // 日期层级筛选
    if (queryForm.dateRange?.length === 2) {
      filter.start_date = queryForm.dateRange[0]
      filter.end_date = queryForm.dateRange[1]
    }

    // 文件类型筛选
    if (queryForm.fileType) {
      advancedFilters.file_type = queryForm.fileType
    }

    // 老化测试筛选条件
    if (queryForm.fileType === 'aging_test' && hasAgingFilters()) {
      advancedFilters.aging_filters = getActiveAgingFilters()
    }

    // 厂测筛选条件
    if (queryForm.fileType === 'factory_test' && hasFactoryFilters()) {
      advancedFilters.factory_filters = getActiveFactoryFilters()
    }

    return { filter, op, advancedFilters }
  }

  // 递归获取所有节点的ID
  const getAllNodeIds = (nodes, ids = []) => {
    nodes.forEach((node) => {
      if (node.id && node.id !== 'root') {
        ids.push(node.id)
      }
      if (node.children && node.children.length > 0) {
        getAllNodeIds(node.children, ids)
      }
    })
    return ids
  }

  // 一键展开/收起所有节点
  const changeExpand = () => {
    if (isExpanded.value) {
      // 当前是展开状态，执行收起
      expandedKeys.value = []
      isExpanded.value = false
    } else {
      // 当前是收起状态，执行展开
      const allIds = getAllNodeIds(treeData.value)
      expandedKeys.value = allIds
      isExpanded.value = true
    }
    // 保存展开状态到localStorage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(expandedKeys.value))
  }

  // 导航历史管理函数

  // 添加到导航历史
  const addToHistory = (path) => {
    if (isNavigatingFromHistory.value) {
      // 如果是从历史导航，不添加到历史
      return
    }

    // 检查是否与当前历史位置的路径相同
    const currentPath = navigationHistory.value[currentHistoryIndex.value]
    if (JSON.stringify(path) === JSON.stringify(currentPath)) {
      return
    }

    // 如果当前不在历史的最后一位，删除后面的历史
    if (currentHistoryIndex.value < navigationHistory.value.length - 1) {
      navigationHistory.value = navigationHistory.value.slice(
        0,
        currentHistoryIndex.value + 1
      )
    }

    // 添加新的路径到历史
    navigationHistory.value.push([...path])
    currentHistoryIndex.value = navigationHistory.value.length - 1

    // 限制历史记录数量（最多50条）
    if (navigationHistory.value.length > 50) {
      navigationHistory.value.shift()
      currentHistoryIndex.value--
    }
  }

  // 后退
  const goBack = () => {
    if (currentHistoryIndex.value > 0) {
      currentHistoryIndex.value--
      isNavigatingFromHistory.value = true

      const targetPath = navigationHistory.value[currentHistoryIndex.value]
      currentNodePath.value = [...targetPath]

      // 更新选中的节点
      if (targetPath.length === 0) {
        selectedKeys.value = []
      } else {
        selectedKeys.value = [targetPath.join('/')]
      }

      // 稍后重置标记
      setTimeout(() => {
        isNavigatingFromHistory.value = false
      }, 100)
    }
  }

  // 前进
  const goForward = () => {
    if (currentHistoryIndex.value < navigationHistory.value.length - 1) {
      currentHistoryIndex.value++
      isNavigatingFromHistory.value = true

      const targetPath = navigationHistory.value[currentHistoryIndex.value]
      currentNodePath.value = [...targetPath]

      // 更新选中的节点
      if (targetPath.length === 0) {
        selectedKeys.value = []
      } else {
        selectedKeys.value = [targetPath.join('/')]
      }

      // 稍后重置标记
      setTimeout(() => {
        isNavigatingFromHistory.value = false
      }, 100)
    }
  }

  // 计算导航状态
  const navigationState = computed(() => {
    return {
      canGoBack: currentHistoryIndex.value > 0,
      canGoForward:
        currentHistoryIndex.value < navigationHistory.value.length - 1,
    }
  })

  // 初始化
  onMounted(() => {
    loadProducts()
    initTreeData()
    loadEntryList()
  })
</script>

<style lang="scss">
  .file-manager-view-content-card-right {
  }

  .file-manager-view-filter-actions {
    text-align: right;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }

  .file-manager-view-more-filters {
    .file-manager-view-filter-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 16px 0;
        font-size: 14px;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;
      }
    }

    .file-manager-view-range-input {
      display: flex;
      align-items: center;
      gap: 8px;

      span {
        font-size: 12px;
        color: #909399;
      }
    }
  }
</style>

<style scoped lang="scss">
  .file-manager-view-container {
    width: 100%;

    .file-manager-view-query-form {
      width: 100%;
    }

    .file-manager-view-content {
      width: 100%;
      min-height: calc(100vh - 240px);
    }

    .file-manager-view-tree-card {
      height: calc(100vh - 172px);

      :deep(.el-card__body) {
        overflow-y: auto;
        height: calc(100vh - 228px);
      }
      :deep() {
        .el-card__header {
          padding: 10px;
        }
      }
    }
  }

  :deep() {
    .el-row {
      display: flex;
      flex-wrap: nowrap;
      position: relative;
    }
  }

  // 防止点击弹窗内容时关闭
  .file-manager-view-filter-popover {
    .el-popover__content {
      padding: 20px;
    }

    // 确保 select 下拉选项在 popover 内部显示
    .el-select-dropdown {
      position: absolute !important;
    }
  }
</style>
