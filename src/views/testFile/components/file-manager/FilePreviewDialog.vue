<template>
  <firefly-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :title="file ? `查看文件：${file.filename || file.name}` : '文件内容'"
    width="70%"
    max-width="1300px"
  >
    <div class="file-preview-dialog-viewer" v-loading="loading">
      <pre v-if="content" style="min-height: 300px">{{ content }}</pre>
      <div v-else class="file-preview-dialog-no-content">
        暂无内容或文件不可预览
      </div>
    </div>
    <template #footer>
      <el-button @click="$emit('update:modelValue', false)">关闭</el-button>
      <el-button type="primary" @click="$emit('download', file)">
        <el-icon><Download /></el-icon>
        下载文件
      </el-button>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import { Download } from '@element-plus/icons-vue'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    file: {
      type: Object,
      default: null,
    },
    content: {
      type: String,
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue', 'download'])
</script>

<style scoped lang="scss">
  .file-preview-dialog-viewer {
    min-height: 300px;
    max-height: 76vh;
    overflow-y: auto;

    pre {
      margin: 0;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 14px;
      line-height: 1.5;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .file-preview-dialog-no-content {
      padding: 50px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
    }
  }
</style>
