<template>
  <div class="redmine-management">
    <el-tabs v-model="activeTab" type="border-card" class="redmine-tabs">
      <el-tab-pane label="状态管理" name="issueStatus">
        <IssueStatusList />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
  import IssueStatusList from './components/issueStatus/IssueStatusList.vue'

  const activeTab = ref('issueStatus')
</script>

<style lang="scss" scoped>
  .redmine-management {
    padding: 16px;
  }

  .disabled-content {
    padding: 40px 0;
    text-align: center;
  }
</style>
