<template>
  <firefly-dialog
    v-model="state.dialogFormVisible"
    :title="state.title"
    width="600px"
    height="auto"
    show-default-button
    @close="close"
    @confirm="save"
  >
    <el-form
      ref="formRef"
      label-width="120px"
      :model="state.form"
      :rules="rules"
    >
      <el-form-item label="状态名称" prop="name">
        <el-input
          v-model="state.form.name"
          placeholder="请输入状态名称"
          maxlength="30"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="是否为关闭状态" prop="is_closed">
        <el-radio-group v-model="state.form.is_closed">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </firefly-dialog>
</template>

<script setup>
  import { doEdit, getOverView } from '@/api/redmine/issueStatus'

  const $baseMessage = inject('$baseMessage')

  const emit = defineEmits(['fetch-data'])

  const state = reactive({
    title: '编辑状态',
    dialogFormVisible: false,
    form: {
      id: 0,
      name: '',
      is_closed: 0,
    },
  })

  const rules = {
    name: [
      { required: true, message: '请输入状态名称', trigger: 'blur' },
      {
        min: 1,
        max: 30,
        message: '状态名称长度在 1 到 30 个字符',
        trigger: 'blur',
      },
    ],
    is_closed: [
      { required: true, message: '请选择是否为关闭状态', trigger: 'change' },
    ],
  }

  const formRef = ref()

  const showEdit = async (row) => {
    if (!row?.id) {
      $baseMessage('无效的状态数据', 'error')
      return
    }

    state.dialogFormVisible = true
    state.title = '编辑状态'

    try {
      const { data } = await getOverView({ id: row.id })
      const statusData = data.data || data
      state.form = {
        id: statusData.id,
        name: statusData.name,
        is_closed: Number(statusData.is_closed),
      }
    } catch (error) {
      console.error('获取状态详情失败:', error)
      $baseMessage('获取状态详情失败', 'error')
    }
  }

  const save = () => {
    formRef.value.validate(async (valid) => {
      if (!valid) {
        return
      }

      try {
        const formData = { ...state.form }

        const { code } = await doEdit(formData)
        if (code === 200) {
          $baseMessage('编辑成功', 'success')
          close()
          emit('fetch-data')
        }
      } catch (error) {
        console.error('保存失败:', error)
        $baseMessage('保存失败', 'error')
      }
    })
  }

  const close = () => {
    state.dialogFormVisible = false
    formRef.value?.resetFields()
  }

  // 暴露供父组件使用
  defineExpose({ showEdit })
</script>

<style lang="scss" scoped>
  .form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
    line-height: 1.4;
  }

  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: flex-start;
  }

  :deep(.el-radio-group) {
    margin-bottom: 4px;
  }

  :deep(.el-switch) {
    margin-bottom: 4px;
  }
</style>
