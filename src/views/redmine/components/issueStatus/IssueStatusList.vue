<template>
  <div class="issue-status-list">
    <vab-query-form>
      <vab-query-form-left-panel :span="18">
        <el-form :inline="true" :model="state.queryForm" @submit.prevent>
          <el-form-item>
            <el-input
              v-model.trim="state.queryForm.name"
              clearable
              placeholder="请输入状态名称"
              style="width: 200px"
            />
          </el-form-item>
          <!-- <el-form-item>
            <el-select
              v-model="state.queryForm.is_closed"
              clearable
              placeholder="状态类型"
              style="width: 120px"
            >
              <el-option label="进行中" :value="0" />
              <el-option label="已关闭" :value="1" />
            </el-select>
          </el-form-item> -->
          <el-form-item>
            <el-select
              v-model="state.queryForm.tracker_type"
              clearable
              placeholder="事项类型"
              style="width: 120px"
            >
              <el-option label="需求" value="requirement" />
              <el-option label="缺陷" value="bug" />
              <el-option label="任务" value="task" />
              <el-option label="产品" value="product" />
            </el-select>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="6">
        <!-- 空白区域 -->
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="state.listLoading"
      border
      :data="state.list"
      :default-sort="state.listSort"
      @sort-change="handleSort"
      size="default"
      height="500"
    >
      <template #empty>
        <CommonEmpty />
      </template>
      <el-table-column label="ID" prop="id" width="80" sortable="custom" />
      <el-table-column
        label="状态名称"
        prop="name"
        min-width="150"
        sortable="custom"
      />
      <el-table-column
        label="是否为关闭状态"
        prop="is_closed"
        width="150"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="row.is_closed ? 'danger' : 'success'">
            {{ row.is_closed ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="事项类型"
        prop="tracker_type"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="getTrackerTypeTagType(row.tracker_type)" size="small">
            {{ getTrackerTypeDisplayLabel(row.tracker_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="100">
        <template #default="{ row }">
          <el-button text @click="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="state.queryForm.pageNo"
      v-model:page-size="state.queryForm.pageSize"
      :layout="state.layout"
      :total="state.total"
      @size-change="sizeChange"
      @current-change="currentChange"
      style="margin-top: 20px"
    />

    <StatusEdit ref="editRef" @fetch-data="fetchData" />
  </div>
</template>

<script setup>
  import CommonEmpty from '@/components/CommonEmpty.vue'
  import StatusEdit from './StatusEdit.vue'
  import { getList } from '@/api/redmine/issueStatus'
  import {
    getTrackerTypeLabel,
    getOrderedTrackerTypes,
  } from '@/config/trackerTypes'
  import { debounce } from 'lodash-es'

  const $baseMessage = inject('$baseMessage')

  // 获取有序的事项类型配置
  const orderedTrackerTypes = getOrderedTrackerTypes()

  // 获取事项类型显示标签
  const getTrackerTypeDisplayLabel = (trackerType) => {
    if (!trackerType || trackerType === null || trackerType === undefined) {
      return '通用'
    }
    return getTrackerTypeLabel(trackerType)
  }

  // 获取事项类型标签样式
  const getTrackerTypeTagType = (trackerType) => {
    if (!trackerType || trackerType === null || trackerType === undefined) {
      return ''
    }

    const typeConfig = orderedTrackerTypes.find(
      (config) => config.value === trackerType
    )
    if (typeConfig) {
      return typeConfig.tagType
    }

    return ''
  }

  const state = reactive({
    list: [],
    listLoading: true,
    listSort: { prop: 'position', order: 'ascending' },
    layout: 'total, sizes, prev, pager, next, jumper',
    total: 0,
    queryForm: {
      pageNo: 1,
      pageSize: 20,
      name: '',
      is_closed: '',
      tracker_type: 'requirement',
      filter: {},
      op: {},
    },
  })

  const editRef = ref()
  const handleEdit = (row) => {
    editRef.value.showEdit(row)
  }

  // 创建防抖的搜索函数
  const debouncedFetchData = debounce(() => {
    state.queryForm.pageNo = 1
    fetchData()
  }, 800)

  const sizeChange = (val) => {
    state.queryForm.pageSize = val
    fetchData()
  }

  const currentChange = (val) => {
    state.queryForm.pageNo = val
    fetchData()
  }

  const handleSort = ({ prop, order }) => {
    state.listSort = { prop, order }

    if (prop && order) {
      const sortOrder = order === 'ascending' ? 'ASC' : 'DESC'
      state.queryForm.sort = prop
      state.queryForm.order = sortOrder
    }

    fetchData()
  }

  const fetchData = async () => {
    state.listLoading = true
    try {
      // 构建filter参数
      const filter = {
        statuses_type: 'system', // 只筛选系统状态
      }
      const op = {
        statuses_type: '=',
      }

      if (state.queryForm.name) {
        filter.name = state.queryForm.name
        op.name = 'LIKE'
      }

      if (state.queryForm.is_closed !== '') {
        filter.is_closed = state.queryForm.is_closed
        op.is_closed = '='
      }

      if (state.queryForm.tracker_type) {
        filter.tracker_type = state.queryForm.tracker_type
        op.tracker_type = '='
      }

      const params = {
        pageNo: state.queryForm.pageNo,
        pageSize: state.queryForm.pageSize,
        filter,
        op,
        sort: state.queryForm.sort || 'position',
        order: state.queryForm.order || 'ASC',
      }

      const { data } = await getList(params)
      state.list = data.data || []
      state.total = data.total || 0
    } catch (error) {
      console.error('获取数据失败:', error)
      $baseMessage('获取数据失败', 'error')
    } finally {
      state.listLoading = false
    }
  }

  // 监听搜索字段变化，使用防抖
  watch(
    () => state.queryForm.name,
    () => {
      debouncedFetchData()
    }
  )

  watch(
    () => state.queryForm.is_closed,
    () => {
      debouncedFetchData()
    }
  )

  watch(
    () => state.queryForm.tracker_type,
    () => {
      debouncedFetchData()
    }
  )

  onMounted(() => {
    fetchData()
  })
</script>

<style lang="scss" scoped>
  .issue-status-list {
    padding: 0;
  }

  .info-text {
    display: flex;
    align-items: center;

    .el-text {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
</style>
