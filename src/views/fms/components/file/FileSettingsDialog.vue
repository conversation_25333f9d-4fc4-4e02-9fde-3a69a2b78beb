<template>
  <firefly-dialog
    v-model="dialogVisible"
    title="文件设置"
    width="518px"
    max-height="60vh"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="文件名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入文件名称"
          maxlength="255"
          show-word-limit
          @keyup.enter="handleSubmit"
        />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入文件描述"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="可见性" prop="visibility">
        <el-radio-group v-model="formData.visibility">
          <el-radio label="public">
            <el-icon><View /></el-icon>
            公开
          </el-radio>
          <el-radio label="department">
            <vab-icon
              icon="department"
              is-custom-svg
              style="width: 22px; height: 22px; margin-right: 0px"
            />
            部门
          </el-radio>
          <el-radio label="user">
            <vab-icon icon="admin-line" />
            用户
          </el-radio>
          <el-radio label="private">
            <vab-icon icon="lock-2-line" />
            私有
          </el-radio>
        </el-radio-group>
        <div class="file-settings-dialog__visibility-help">
          <el-text size="small" type="info">
            公开：所有人可见；部门：仅指定部门内可见；用户：仅指定用户可见；私有：仅创建者可见
          </el-text>
        </div>
      </el-form-item>

      <!-- 用户选择 -->
      <el-form-item
        v-if="formData.visibility === 'user'"
        label="选择用户"
        prop="selectedUsers"
      >
        <template
          v-if="formData.selectedUsers && formData.selectedUsers.length > 0"
        >
          <div class="file-settings-dialog__user-info">
            <span
              v-for="(user, index) in formData.selectedUsers"
              :key="index"
              class="file-settings-dialog__user-item member-item"
            >
              {{ user.name }}
              <vab-icon icon="close-fill" @click="removeUser(user.id)" />
            </span>
          </div>
        </template>
        <vab-icon
          icon="add-circle-fill"
          style="font-size: 20px; color: #1890ff; cursor: pointer"
          @click="handleUserSelect"
        />
      </el-form-item>

      <!-- 部门选择 -->
      <el-form-item
        v-if="formData.visibility === 'department'"
        label="选择部门"
        prop="selectedDepartments"
      >
        <el-cascader
          v-model="formData.selectedDepartments"
          :options="departmentOptions"
          clearable
          filterable
          collapse-tags
          :show-all-levels="false"
          :props="{
            expandTrigger: 'hover',
            multiple: true,
            value: 'id',
            label: 'name',
            emitPath: false,
          }"
          placeholder="请选择部门"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 私有用户显示 -->
      <el-form-item
        v-if="formData.visibility === 'private'"
        label="选择用户"
        prop="privateUser"
      >
        <el-input
          v-model="currentUserName"
          readonly
          disabled
          placeholder="当前用户"
        >
          <template #prefix>
            <vab-icon icon="admin-line" />
          </template>
        </el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="file-settings-dialog__footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="isSubmitting" @click="handleSubmit">
          保存设置
        </el-button>
      </div>
    </template>
    <!-- 用户选择组件 -->
    <MemberSelect
      v-model:visible="showMemberVisible"
      :user-id-arr="state.userRedmineIdArr"
      @confirm="addMember"
    />
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import { View, Hide, User, OfficeBuilding } from '@element-plus/icons-vue'
  import { storeToRefs } from 'pinia'
  import { useUserStore } from '@/store/modules/user'
  import MemberSelect from '@/components/MemberSelect'
  import { getDepartment } from '@/api/oaReport'
  import { queryPersonnel } from '@/api/user'
  import * as fileApi from '@/api/fms/files'
  import { usePermissionStore } from '@/views/fms/stores/permissionStore'

  const permissionStore = usePermissionStore()

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    fileId: {
      type: [Number, String],
      default: null,
    },
    fileName: {
      type: String,
      default: '',
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'success'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const userStore = useUserStore()
  const { user_id, username } = storeToRefs(userStore)

  // 响应式数据
  const formRef = ref()
  const isSubmitting = ref(false)
  const departmentOptions = ref([])
  const showMemberVisible = ref(false)
  const state = reactive({
    userIdArr: [],
    userRedmineIdArr: [],
  })

  const formData = reactive({
    name: '',
    description: '',
    visibility: 'public',
    selectedUsers: [],
    selectedDepartments: [],
  })

  // 当前用户名称
  const currentUserName = computed(() => {
    return username.value || '当前用户'
  })

  // 表单验证规则
  const formRules = {
    name: [
      { required: true, message: '请输入文件名称', trigger: 'blur' },
      {
        min: 1,
        max: 255,
        message: '文件名称长度在 1 到 255 个字符',
        trigger: 'blur',
      },
    ],
    visibility: [
      { required: true, message: '请选择可见性', trigger: 'change' },
    ],
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        loadFileData()
      }
    }
  )

  // 方法
  const resetForm = () => {
    formData.name = ''
    formData.description = ''
    formData.visibility = 'public'
    formData.selectedUsers = []
    formData.selectedDepartments = []

    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  // 用户选择相关方法
  const handleUserSelect = () => {
    // 设置已选用户ID数组
    state.userRedmineIdArr = formData.selectedUsers.map(
      (user) => user.third.third_user_id
    )
    showMemberVisible.value = true
  }

  const removeUser = (userId) => {
    formData.selectedUsers = formData.selectedUsers.filter(
      (user) => user.id !== userId
    )
  }

  const addMember = (options) => {
    let users = options.userList ?? []
    // 添加选中的用户到formData.selectedUsers
    formData.selectedUsers = users
      .filter((user) => user.biz_mail)
      ?.map((user) => ({ ...user }))

    showMemberVisible.value = false
  }

  // 监听可见性变化，清空相关选择
  watch(
    () => formData.visibility,
    (newVisibility) => {
      if (newVisibility === 'user') {
        formData.selectedDepartments = []
      } else if (newVisibility === 'department') {
        formData.selectedUsers = []
      } else if (newVisibility === 'private') {
        formData.selectedUsers = []
        formData.selectedDepartments = []
      } else {
        formData.selectedUsers = []
        formData.selectedDepartments = []
      }
    }
  )

  // 根据传入Ids获取用户数据
  const getUserData = async (userIds) => {
    const { data } = await queryPersonnel()

    // 递归遍历树形结构，提取所有用户数据
    const extractUsers = (departments) => {
      let users = []

      departments.forEach((dept) => {
        // 如果部门有children（用户），添加到用户列表
        if (dept.children && Array.isArray(dept.children)) {
          dept.children.forEach((user) => {
            // 检查是否为用户对象（有workwx_userid字段）
            if (user.workwx_userid) {
              users.push(user)
            } else if (user.children) {
              // 如果是子部门，递归处理
              users = users.concat(extractUsers([user]))
            }
          })
        }
      })

      return users
    }

    // 提取所有用户
    const allUsers = extractUsers(data)

    // 如果传入了userIds，则过滤返回指定用户
    if (userIds && userIds.length > 0) {
      return allUsers.filter((user) => userIds.includes(user.id))
    }

    // 否则返回所有用户
    return allUsers
  }

  // 获取部门数据
  const fetchDataDepart = async () => {
    try {
      let query = {
        filter: {
          parentid: 0,
        },
        op: {
          parentid: '>',
        },
      }
      const { data } = await getDepartment(query)
      departmentOptions.value = data
    } catch (error) {
      console.error('获取部门数据失败:', error)
      $baseMessage('获取部门数据失败', 'error', 'vab-hey-message-error')
    }
  }

  const loadFileData = async () => {
    if (!props.fileId) {
      resetForm()
      return
    }

    try {
      // 获取文件详情
      const { data } = await fileApi.show(props.fileId)

      const file = data

      if (file) {
        let Users = []
        if (file.allow?.user) {
          Users = await getUserData(file.allow.user)
        }

        formData.name = file.name || ''
        formData.description = file.description || ''
        formData.visibility = file.visibility || 'public'
        formData.selectedUsers = Users || []
        formData.selectedDepartments = file.allow?.dept || []
      } else {
        // 如果无法获取详情，至少设置名称
        formData.name = props.fileName || ''
        formData.description = ''
        formData.visibility = 'public'
      }
    } catch (error) {
      console.error('加载文件数据失败:', error)
      // 降级处理，使用传入的名称
      formData.name = props.fileName || ''
      formData.description = ''
      formData.visibility = 'public'
    }
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      isSubmitting.value = true

      // 构造权限数组
      let permissions = []
      if (formData.visibility === 'user') {
        permissions = formData.selectedUsers.map((user) => ({
          subject_type: 'user',
          subject_id: user.id,
          subject_name: user.name,
          permission_set: permissionStore.PERMISSION_BITS.VIEW,
        }))
      } else if (formData.visibility === 'department') {
        // 需要获取部门名称，从departmentOptions中查找
        permissions = formData.selectedDepartments.map((deptId) => {
          const findDeptName = (options, id) => {
            for (const option of options) {
              if (option.id === id) {
                return option.name
              }
              if (option.children) {
                const found = findDeptName(option.children, id)
                if (found) return found
              }
            }
            return null
          }
          const deptName = findDeptName(departmentOptions.value, deptId)
          return {
            subject_type: 'dept',
            subject_id: deptId,
            subject_name: deptName || '未知部门',
            permission_set: permissionStore.PERMISSION_BITS.VIEW,
          }
        })
      } else if (formData.visibility === 'private') {
        permissions = [
          {
            subject_type: 'user',
            subject_id: user_id.value,
            subject_name: currentUserName.value,
            permission_set: permissionStore.PERMISSION_BITS.VIEW,
          },
        ]
      }

      // 更新文件
      const updateData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        visibility: formData.visibility,
        permissions: JSON.stringify(permissions), // 序列化为JSON字符串
      }

      const updatedFile = await fileApi.update(props.fileId, updateData)

      $baseMessage('文件设置更新成功', 'success', 'vab-hey-message-success')

      emit('success', updatedFile)
      handleClose()
    } catch (error) {
      console.error(error)
      $baseMessage(
        '更新文件设置失败：' + (error.message || '未知错误'),
        'error',
        'vab-hey-message-error'
      )
    } finally {
      isSubmitting.value = false
    }
  }

  const handleClose = () => {
    dialogVisible.value = false
  }

  // 组件挂载时获取部门数据
  onMounted(() => {
    fetchDataDepart()
  })
</script>

<style lang="scss" scoped>
  .file-settings-dialog {
    &__visibility-help {
      margin-top: 4px;
    }

    &__footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }

    &__user-info {
      display: flex;
      flex-flow: wrap;
      align-items: center;
    }

    &__user-item {
      padding: 7px 10px 5px 10px;
      margin-right: 10px;
      margin-bottom: 5px;
      font-size: 14px;
      line-height: 14px;
      color: #333333;
      background: #f2f4f6;
      border-radius: 4px;

      .vab-icon {
        cursor: pointer;
        font-size: 14px;
        color: var(--el-color-primary-light-3);
        transition: color 0.2s;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-radio) {
    display: flex;
    align-items: center;
    margin-right: 24px;
    margin-bottom: 8px;

    .el-radio__label {
      display: flex;
      align-items: center;
      gap: 4px;
      padding-left: 8px;
    }
  }
</style>
