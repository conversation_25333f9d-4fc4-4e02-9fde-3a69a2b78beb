<template>
  <firefly-dialog
    v-model="dialogVisible"
    :title="'文件预览'"
    width="80%"
    :close-on-click-modal="false"
    custom-class="file-preview-dialog"
    :destroy-on-close="false"
    :never-close="true"
    @close="handleClose"
  >
    <div class="file-preview" v-loading="isLoading">
      <div class="file-preview__header">
        <div class="file-preview__info">
          <div class="file-preview__name">{{ file?.name }}</div>
          <div class="file-preview__meta">
            <span>文件体积：{{ formatFileSize(file?.size) }}</span>
            <span>创建时间：{{ formatDateTime(file?.created_at) }}</span>
          </div>
        </div>
        <div class="file-preview__actions">
          <el-button
            type="text"
            :icon="Download"
            @click="handleDownload"
            v-fms-permission="{
              targetType: 'file',
              targetId: file?.id,
              permission: FMS_PERMISSIONS.VIEW,
            }"
          >
            下载
          </el-button>
          <!-- <el-button type="text" :icon="Share" @click="handleShare">
            分享
          </el-button> -->
          <!-- <el-button type="text" :icon="FullScreen" @click="toggleFullscreen">
            全屏
          </el-button> -->
          <el-button type="text" :icon="Download" @click="handleDownload">
            下载
          </el-button>
        </div>
      </div>

      <div class="file-preview__content" ref="previewContentRef">
        <!-- 图片预览 -->
        <div v-if="previewType === 'image'" class="file-preview__image">
          <el-image
            :src="previewUrl"
            :alt="file?.name"
            fit="contain"
            :preview-src-list="[previewUrl]"
            :initial-index="0"
            class="file-preview__image-viewer"
          />
        </div>

        <!-- 视频预览 -->
        <div v-else-if="previewType === 'video'" class="file-preview__video">
          <video
            ref="videoRef"
            :src="previewUrl"
            controls
            preload="metadata"
            class="file-preview__video-player"
          >
            您的浏览器不支持视频播放
          </video>
        </div>

        <!-- 音频预览 -->
        <div v-else-if="previewType === 'audio'" class="file-preview__audio">
          <div class="file-preview__audio-cover">
            <el-icon><Headset /></el-icon>
            <div class="file-preview__audio-title">{{ file?.name }}</div>
          </div>
          <audio
            ref="audioRef"
            :src="previewUrl"
            controls
            preload="metadata"
            class="file-preview__audio-player"
          >
            您的浏览器不支持音频播放
          </audio>
        </div>

        <!-- PDF预览 -->
        <div v-else-if="previewType === 'pdf'" class="file-preview__pdf">
          <iframe
            :src="previewUrl"
            class="file-preview__pdf-viewer"
            frameborder="0"
          />
        </div>

        <!-- 文本预览 -->
        <div v-else-if="previewType === 'text'" class="file-preview__text">
          <pre class="file-preview__text-content">{{ textContent }}</pre>
        </div>

        <!-- 代码预览 -->
        <div v-else-if="previewType === 'code'" class="file-preview__code">
          <div class="file-preview__code-header">
            <span class="file-preview__code-language">
              {{ getLanguage(file?.extension) }}
            </span>
            <el-button type="text" size="small" @click="copyCode">
              复制代码
            </el-button>
          </div>
          <pre
            class="file-preview__code-content"
          ><code>{{ textContent }}</code></pre>
        </div>

        <!-- Office文档预览 -->
        <div
          v-else-if="previewType === 'office' && previewUrl && !isDestroyed"
          class="file-preview__office"
        >
          <office-preview
            v-if="!isDestroyed && previewUrl"
            :file-url="previewUrl"
            :file-type="file?.name?.split('.').pop()?.toLowerCase()"
            :dialog-visible="true"
            :preview-mode="'binary'"
          />
        </div>

        <!-- 不支持预览 -->
        <div v-else class="file-preview__unsupported">
          <div class="file-preview__unsupported-placeholder">
            <el-icon><Files /></el-icon>
            <div class="file-preview__unsupported-title">无法预览此文件</div>
            <div class="file-preview__unsupported-hint">
              该文件类型暂不支持在线预览，请下载后查看
            </div>
            <el-button type="primary" @click="handleDownload">
              下载文件
            </el-button>
          </div>
        </div>
      </div>

      <!-- 文件信息面板 -->
    </div>

    <template #footer>
      <div class="file-preview__footer">
        <div class="file-preview__footer-left">
          <el-popover
            placement="top-start"
            :width="488"
            trigger="click"
            popper-class="file-preview__info-popover"
          >
            <template #reference>
              <el-button type="text" :icon="InfoFilled">文件信息</el-button>
            </template>
            <div class="file-preview__info-content">
              <div class="file-preview__info-item">
                <label>文件名：</label>
                <span>{{ file?.name }}</span>
              </div>
              <div class="file-preview__info-item">
                <label>大小：</label>
                <span>{{ formatFileSize(file?.size) }}</span>
              </div>
              <div class="file-preview__info-item">
                <label>类型：</label>
                <span>{{ file?.mime_type }}</span>
              </div>
              <div class="file-preview__info-item">
                <label>创建时间：</label>
                <span>{{ formatDateTime(file?.created_at) }}</span>
              </div>
              <div class="file-preview__info-item">
                <label>修改时间：</label>
                <span>{{ formatDateTime(file?.updated_at) }}</span>
              </div>
              <div
                v-if="file?.tags?.length > 0"
                class="file-preview__info-item"
              >
                <label>标签：</label>
                <div class="file-preview__tags">
                  <el-tag
                    v-for="tag in file.tags"
                    :key="tag.id"
                    size="small"
                    :color="tag.color"
                  >
                    {{ tag.name }}
                  </el-tag>
                </div>
              </div>
              <div v-if="file?.description" class="file-preview__info-item">
                <label>描述：</label>
                <span>{{ file.description }}</span>
              </div>
            </div>
          </el-popover>
        </div>
        <div class="file-preview__footer-right">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    nextTick,
    inject,
    defineProps,
    defineEmits,
    onUnmounted,
  } from 'vue'
  import {
    Download,
    Share,
    FullScreen,
    Close,
    InfoFilled,
    Headset,
    Document,
    Files,
  } from '@element-plus/icons-vue'
  import { useFileStore } from '../../stores/fileStore'
  import { useFmsStore } from '../../stores/fmsStore'
  import * as fileApi from '@/api/fms/files'
  import { useFmsPermission, vFmsPermission } from '@/utils/bmsPermission'
  import OfficePreview from '@/components/OfficePreview.vue'
  import _ from 'lodash'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    file: {
      type: Object,
      default: null,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const fileStore = useFileStore()
  const fmsStore = useFmsStore()
  const { FMS_PERMISSIONS } = useFmsPermission()

  // 响应式数据
  const previewContentRef = ref()
  const videoRef = ref()
  const audioRef = ref()
  const isLoading = ref(false)
  const showInfo = ref(false)
  const textContent = ref('')
  const previewUrl = ref('')

  // 添加组件销毁标识和请求控制器
  const isDestroyed = ref(false)
  const abortController = ref(null)

  const getType = () => {
    if (!props.file) return 'unsupported'

    const mime = props.file.mime_type || ''
    const extension = props.file.name?.split('.').pop()?.toLowerCase()

    // zip 类型优先处理
    if (
      mime === 'application/zip' ||
      mime === 'application/x-zip-compressed' ||
      extension === 'zip'
    ) {
      return 'unsupported'
    }

    if (mime.startsWith('image/')) return 'image'
    if (mime.startsWith('video/')) return 'video'
    if (mime.startsWith('audio/')) return 'audio'

    if (mime === 'application/pdf' || extension === 'pdf') return 'pdf'

    if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(extension)) {
      return 'office'
    }

    // 关键：支持所有 text/* 的 MIME
    if (mime.startsWith('text/')) return 'text'

    if (['txt', 'md', 'json', 'xml', 'csv'].includes(extension)) {
      return 'text'
    }

    if (
      ['js', 'ts', 'vue', 'java', 'py', 'php', 'c', 'cpp'].includes(extension)
    ) {
      return 'code'
    }

    return 'unsupported'
  }

  const previewType = ref('unsupported')

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible && props.file && !isDestroyed.value) {
        loadPreview()
        previewType.value = getType()
      }
    }
  )

  // 监听文件变化
  watch(
    () => props.file,
    (file) => {
      if (file && props.visible && !isDestroyed.value) {
        loadPreview()
      }
    }
  )

  // 方法
  const loadPreview = _.debounce(async () => {
    if (!props.file || isDestroyed.value) return

    try {
      // 取消之前的请求
      if (abortController.value) {
        abortController.value.abort()
      }

      // 创建新的请求控制器
      abortController.value = new AbortController()

      isLoading.value = true

      // 检查组件是否已销毁
      if (isDestroyed.value) return

      // 获取预览URL
      previewUrl.value = await fileStore.getPreviewUrl(props.file.id)
      previewUrl.value = previewUrl.value ?? ''

      // 再次检查组件是否已销毁
      if (isDestroyed.value) return

      // 如果是文本或代码文件，加载内容
      if (previewType.value === 'text' || previewType.value === 'code') {
        textContent.value = await fileStore.getTextContent(previewUrl.value)
      }
    } catch (error) {
      // 如果是取消请求的错误，不显示错误信息
      if (error.name === 'AbortError' || isDestroyed.value) {
        return
      }
      console.error('loadPreview error:', error)
    } finally {
      if (!isDestroyed.value) {
        isLoading.value = false
      }
    }
  }, 20)

  const handleDownload = async () => {
    if (isDestroyed.value) return

    try {
      const result = await fileApi.download(props.file.id)

      // 检查组件是否已销毁
      if (isDestroyed.value) return

      const link = document.createElement('a')
      link.href = result.url
      link.download = result.filename
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 延迟清理URL，确保下载开始
      setTimeout(() => {
        window.URL.revokeObjectURL(result.url)
      }, 1000)

      if (!isDestroyed.value) {
        $baseMessage(
          `文件 "${result.filename}" 下载开始`,
          'success',
          'vab-hey-message-success'
        )
      }
    } catch (error) {
      if (!isDestroyed.value) {
        $baseMessage(
          `下载失败: ${error.message}`,
          'error',
          'vab-hey-message-error'
        )
      }
    }
  }

  const handleShare = () => {
    if (isDestroyed.value) return

    // TODO: 实现文件分享
    $baseMessage('文件分享功能开发中', 'info', 'vab-hey-message-info')
  }

  const toggleFullscreen = () => {
    if (!previewContentRef.value || isDestroyed.value) return

    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      previewContentRef.value.requestFullscreen()
    }
  }

  const copyCode = async () => {
    if (isDestroyed.value) return

    try {
      await navigator.clipboard.writeText(textContent.value)
      $baseMessage('代码已复制到剪贴板', 'success', 'vab-hey-message-success')
    } catch (error) {
      $baseMessage('复制失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleClose = async () => {
    // 标记组件即将销毁
    isDestroyed.value = true

    // 取消所有进行中的请求
    if (abortController.value) {
      abortController.value.abort()
    }

    // 停止媒体播放
    if (videoRef.value) {
      videoRef.value.pause()
    }
    if (audioRef.value) {
      audioRef.value.pause()
    }

    // 清理状态
    previewUrl.value = ''
    textContent.value = ''
    isLoading.value = false

    await new Promise((resolve) => setTimeout(resolve, 100))

    dialogVisible.value = false

    // 重置销毁标识，为下次打开做准备
    await new Promise((resolve) => setTimeout(resolve, 200))
    isDestroyed.value = false
  }

  // 组件卸载时的清理
  onUnmounted(() => {
    isDestroyed.value = true

    // 取消所有进行中的请求
    if (abortController.value) {
      abortController.value.abort()
    }

    // 停止媒体播放
    if (videoRef.value) {
      videoRef.value.pause()
    }
    if (audioRef.value) {
      audioRef.value.pause()
    }

    // 清理状态
    previewUrl.value = ''
    textContent.value = ''
    isLoading.value = false
  })

  // 工具方法
  const formatFileSize = (bytes) => {
    return fmsStore.formatFileSize(bytes)
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return dateTime
  }

  const getLanguage = (extension) => {
    const languageMap = {
      js: 'JavaScript',
      ts: 'TypeScript',
      vue: 'Vue',
      html: 'HTML',
      css: 'CSS',
      scss: 'SCSS',
      less: 'Less',
      json: 'JSON',
      xml: 'XML',
      md: 'Markdown',
      py: 'Python',
      java: 'Java',
      php: 'PHP',
      go: 'Go',
      rs: 'Rust',
      cpp: 'C++',
      c: 'C',
      sh: 'Shell',
      sql: 'SQL',
    }
    return (
      languageMap[extension?.toLowerCase()] ||
      extension?.toUpperCase() ||
      'Text'
    )
  }
</script>

<style lang="scss" scoped>
  .file-preview {
    display: flex;
    flex-direction: column;
    height: 70vh;

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;
      border-bottom: 1px solid var(--el-border-color-light);
    }

    &__info {
      flex: 1;
    }

    &__name {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }

    &__meta {
      font-size: 13px;
      color: var(--el-text-color-regular);

      span {
        margin-right: 16px;
      }
    }

    &__actions {
      display: flex;
      gap: 8px;
    }

    &__content {
      flex: 1;
      display: flex;
      overflow: hidden;
      margin: 16px 0;
    }

    // 图片预览
    &__image {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      &-viewer {
        max-width: 100%;
        max-height: 100%;
      }
    }

    // 视频预览
    &__video {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      &-player {
        max-width: 100%;
        max-height: 100%;
      }
    }

    // 音频预览
    &__audio {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &-cover {
        text-align: center;
        margin-bottom: 20px;

        .el-icon {
          font-size: 64px;
          color: var(--el-color-primary);
          margin-bottom: 12px;
        }
      }

      &-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 20px;
      }

      &-player {
        width: 100%;
        max-width: 400px;
      }
    }

    // PDF预览
    &__pdf {
      flex: 1;

      &-viewer {
        width: 100%;
        height: 100%;
      }
    }

    // 文本预览
    &__text {
      flex: 1;
      overflow: auto;

      &-content {
        min-height: 256px;
        padding: 16px;
        margin: 0;
        background: var(--el-bg-color-page);
        border-radius: 6px;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 13px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }

    // 代码预览
    &__code {
      flex: 1;
      display: flex;
      flex-direction: column;

      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 16px;
        background: var(--el-bg-color-page);
        border-radius: 6px 6px 0 0;
        border-bottom: 1px solid var(--el-border-color-light);
      }

      &-language {
        font-size: 12px;
        font-weight: 500;
        color: var(--el-text-color-regular);
      }

      &-content {
        flex: 1;
        padding: 16px;
        margin: 0;
        background: var(--el-bg-color-page);
        border-radius: 0 0 6px 6px;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 13px;
        line-height: 1.5;
        overflow: auto;

        code {
          background: none;
          padding: 0;
          color: inherit;
        }
      }
    }

    // Office文档和不支持预览的占位符
    &__office,
    &__unsupported {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      &-placeholder {
        text-align: center;

        .el-icon {
          font-size: 64px;
          color: var(--el-text-color-placeholder);
          margin-bottom: 16px;
        }
      }

      &-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 8px;
      }

      &-hint {
        font-size: 14px;
        color: var(--el-text-color-regular);
        margin-bottom: 20px;
      }
    }

    // 侧边栏
    &__sidebar {
      width: 300px;
      border-left: 1px solid var(--el-border-color-light);
      display: flex;
      flex-direction: column;

      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: var(--el-bg-color-page);
        border-bottom: 1px solid var(--el-border-color-lighter);
        font-weight: 500;
      }

      &-content {
        flex: 1;
        padding: 16px;
        overflow-y: auto;
      }
    }

    &__info-item {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        font-weight: 500;
        color: var(--el-text-color-regular);
        margin-bottom: 4px;
      }

      span {
        font-size: 14px;
        color: var(--el-text-color-primary);
        word-break: break-all;
      }
    }

    &__tags {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    &__footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-light);

      &-left,
      &-right {
        display: flex;
        gap: 8px;
      }
    }
  }

  // 全局样式覆盖
  :global(.file-preview-dialog) {
    .el-dialog__body {
      padding: 20px;
    }
  }
</style>
