<template>
  <firefly-dialog
    v-model="visible"
    title="Wiki同步"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="fms-wiki-sync-dialog">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="Wiki空间" prop="spaceId">
          <el-select
            v-model="form.spaceId"
            placeholder="请选择要同步的Wiki空间"
            style="width: 100%"
            :loading="spaceLoading"
            clearable
          >
            <el-option
              v-for="space in spaceList"
              :key="space.space_id"
              :label="space.space_name"
              :value="space.space_id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="同步说明">
          <div class="fms-wiki-sync-description">
            <p>• 将选择的空间中所有文档的文件同步到根目录</p>
            <p>• 保持Wiki原有的目录层级结构</p>
            <p>• 自动提取文档中的附件文件</p>
            <p>• 同步过程可能需要一些时间，请耐心等待</p>
          </div>
        </el-form-item>
      </el-form>

      <!-- 同步进度显示 -->
      <div v-if="syncing" class="fms-wiki-sync-progress">
        <el-progress
          :percentage="syncProgress"
          :status="syncStatus"
          :stroke-width="8"
        />
        <p class="fms-wiki-sync-progress-text">{{ syncProgressText }}</p>
      </div>
    </div>

    <template #footer>
      <div class="fms-wiki-sync-dialog-footer">
        <el-button @click="handleClose" :disabled="syncing">
          {{ syncing ? '同步中...' : '取消' }}
        </el-button>
        <el-button
          type="primary"
          @click="handleSync"
          :loading="syncing"
          :disabled="!form.spaceId || syncing"
        >
          {{ syncing ? '同步中...' : '开始同步' }}
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, inject } from 'vue'
  import { getWikiSpaceList } from '@/api/biWiki'
  import { syncWiki } from '@/api/fms/directories'

  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    parentDirectoryId: {
      type: [Number, String],
      default: null,
    },
  })

  const emit = defineEmits(['update:visible', 'success'])

  // 响应式数据
  const formRef = ref()
  const spaceList = ref([])
  const spaceLoading = ref(false)
  const syncing = ref(false)
  const syncProgress = ref(0)
  const syncStatus = ref('')
  const syncProgressText = ref('')
  const syncResult = ref(null)

  const form = reactive({
    spaceId: null,
  })

  const rules = {
    spaceId: [{ required: true, message: '请选择Wiki空间', trigger: 'change' }],
  }

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  // 获取Wiki空间列表
  const loadSpaceList = async () => {
    try {
      spaceLoading.value = true
      const res = await getWikiSpaceList({})
      spaceList.value = res.data.data || []
    } catch (error) {
      console.error('获取Wiki空间列表失败:', error)
      $baseMessage('获取Wiki空间列表失败', 'error', 'vab-hey-message-error')
      spaceList.value = []
    } finally {
      spaceLoading.value = false
    }
  }

  // 开始同步
  const handleSync = async () => {
    try {
      // 表单验证
      const valid = await formRef.value.validate()
      if (!valid) return

      // 确认对话框
      $baseConfirm(`确认要将Wiki空间同步到当前目录吗`, null, async () => {
        await performSync()
      })
    } catch (error) {
      console.error('同步验证失败:', error)
    }
  }

  // 执行同步
  const performSync = async () => {
    try {
      syncing.value = true
      syncProgress.value = 0
      syncStatus.value = ''
      syncProgressText.value = '正在初始化同步...'
      syncResult.value = null

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        if (syncProgress.value < 90) {
          syncProgress.value = Math.min(
            90,
            syncProgress.value + Math.floor(Math.random() * 10)
          )
          syncProgressText.value = `正在同步Wiki空间数据... ${syncProgress.value}%`
        }
      }, 500)

      // 调用同步API
      const response = await syncWiki({
        space_id: form.spaceId,
        parent_directory_id: props.parentDirectoryId,
      })

      clearInterval(progressInterval)
      syncProgress.value = 100
      syncStatus.value = response.success ? 'success' : 'exception'
      syncProgressText.value = response.success ? '同步完成！' : '同步失败'
      syncResult.value = response

      if (response.success) {
        await new Promise((resolve) => setTimeout(resolve, 1000))
        emit('success')
      } else {
        $baseMessage(
          response.message || '同步失败',
          'error',
          'vab-hey-message-error'
        )
      }
    } catch (error) {
      console.error('Wiki同步失败:', error)
      syncProgress.value = 100
      syncStatus.value = 'exception'
      syncProgressText.value = '同步失败'
      $baseMessage('Wiki同步失败', 'error', 'vab-hey-message-error')
    } finally {
      syncing.value = false
    }
  }

  // 关闭对话框
  const handleClose = () => {
    if (syncing.value) {
      $baseMessage('同步进行中，无法关闭', 'warning', 'vab-hey-message-warning')
      return
    }

    visible.value = false
    // 重置表单和状态
    form.spaceId = null
    syncProgress.value = 0
    syncStatus.value = ''
    syncProgressText.value = ''
    syncResult.value = null
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  // 组件挂载时加载空间列表
  onMounted(() => {
    loadSpaceList()
  })
</script>

<style scoped>
  .fms-wiki-sync-dialog {
    padding: 20px 0;
  }

  .fms-wiki-sync-description {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #409eff;
  }

  .fms-wiki-sync-description p {
    margin: 5px 0;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }

  .fms-wiki-sync-progress {
    margin-top: 20px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 4px;
  }

  .fms-wiki-sync-progress-text {
    margin-top: 10px;
    text-align: center;
    color: #606266;
    font-size: 14px;
  }

  .fms-wiki-sync-result {
    margin-top: 20px;
    padding: 20px;
    background-color: #f0f9ff;
    border-radius: 4px;
    border: 1px solid #b3d8ff;
  }

  .fms-wiki-sync-result-summary h4 {
    margin-bottom: 10px;
    color: #409eff;
    font-size: 16px;
  }

  .fms-wiki-sync-result-summary p {
    margin: 5px 0;
    color: #606266;
    font-size: 14px;
  }

  .fms-wiki-sync-failures {
    margin-top: 15px;
  }

  .fms-wiki-sync-failures h4 {
    margin-bottom: 10px;
    color: #f56c6c;
    font-size: 16px;
  }

  .fms-wiki-sync-failures ul {
    margin: 0;
    padding-left: 20px;
  }

  .fms-wiki-sync-failures li {
    margin: 5px 0;
    color: #f56c6c;
    font-size: 14px;
  }

  .fms-wiki-sync-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
</style>
