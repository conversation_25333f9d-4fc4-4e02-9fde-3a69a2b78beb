<template>
  <el-dialog
    v-model="visible"
    :title="`移动${item?.type === 'directory' ? '文件夹' : '文件'}`"
    width="600px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="目标位置" prop="targetDirectoryId">
        <el-tree-select
          v-model="form.targetDirectoryId"
          :data="directoryTree"
          :props="treeProps"
          placeholder="选择目标文件夹"
          check-strictly
          :render-after-expand="false"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, reactive, watch, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import * as directoryApi from '@/api/fms/directories'
  import * as fileApi from '@/api/fms/files'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: null,
    },
  })

  const emit = defineEmits(['update:modelValue', 'success'])

  const visible = ref(false)
  const loading = ref(false)
  const formRef = ref()
  const directoryTree = ref([])

  const form = reactive({
    targetDirectoryId: null,
  })

  const rules = {
    targetDirectoryId: [
      { required: true, message: '请选择目标文件夹', trigger: 'change' },
    ],
  }

  const treeProps = {
    children: 'children',
    label: 'name',
    value: 'id',
  }

  // 监听显示状态
  watch(
    () => props.modelValue,
    (val) => {
      visible.value = val
      if (val) {
        form.targetDirectoryId = null
        loadDirectoryTree()
      }
    }
  )

  watch(visible, (val) => {
    emit('update:modelValue', val)
  })

  const loadDirectoryTree = async () => {
    try {
      const response = await directoryApi.tree()
      directoryTree.value = [
        {
          id: null,
          name: '根目录',
          children: response.data || [],
        },
      ]
    } catch (error) {
      ElMessage.error('加载目录树失败：' + error.message)
    }
  }

  const handleClose = () => {
    visible.value = false
    form.targetDirectoryId = null
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      const moveData = {
        newParentId: form.targetDirectoryId,
      }

      if (props.item?.type === 'directory') {
        await directoryApi.move(props.item.id, moveData)
      } else {
        await fileApi.move(props.item.id, {
          directory_id: form.targetDirectoryId,
        })
      }

      ElMessage.success('移动成功')
      emit('success')
      handleClose()
    } catch (error) {
      if (error.message) {
        ElMessage.error('移动失败：' + error.message)
      }
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    loadDirectoryTree()
  })
</script>
