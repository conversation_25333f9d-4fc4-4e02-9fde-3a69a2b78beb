<template>
  <firefly-dialog
    width="1000px"
    height="712px"
    top="5%"
    v-model="visible"
    :destroy-on-close="false"
    :append-to-body="true"
    title="文件管理系统搜索结果"
    class="fms-search-dialog"
    :before-close="handleClose"
  >
    <div class="fms-search-container">
      <!-- 搜索框 -->
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-input
            v-model="searchText"
            placeholder="搜索全部文件和目录"
            style="width: 300px"
            @keyup.enter="fetchSearchResults"
          >
            <template #prefix>
              <el-icon class="el-input__icon"><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-select
            v-model="typeFilter"
            clearable
            placeholder="选择类型"
            style="width: 120px"
            @change="fetchSearchResults"
          >
            <el-option label="全部" value="" />
            <el-option label="文件" value="file" />
            <el-option label="目录" value="directory" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <PersonnelSelect
            v-model:value="selectedCreators"
            :option-list="userList"
            :multiple="true"
            placeholder-text="请选择创建人"
            style="width: 184px"
            @change="handleCreatorChange"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchSearchResults">搜索</el-button>
        </el-form-item>
      </el-form>

      <!-- 搜索结果 -->
      <div v-loading="loading" class="fms-search-results">
        <div v-if="searchResult.length === 0 && !loading" class="no-results">
          <el-empty description="没有找到匹配的文件或目录" />
        </div>

        <div v-else class="result-list">
          <div
            v-for="(item, index) in searchResult"
            :key="index"
            class="fms-search-item"
            @click="openItem(item)"
          >
            <div class="fms-search-item-header">
              <div class="fms-search-item-icon">
                <!-- 目录图标 -->
                <!-- <el-icon
                  v-if="item.type === 'directory'"
                  class="directory-icon"
                >
                  <Folder />
                </el-icon> -->
                <vab-icon
                  v-if="item.type === 'directory'"
                  :icon="'icon-dir'"
                  is-custom-svg
                  class="fms-search-item-svg-icon"
                />
                <!-- 图片文件显示缩略图 -->
                <el-image
                  v-else-if="isImageFile(item) && showThumbnail"
                  :src="getThumbnailUrl(item)"
                  :alt="item.name"
                  fit="cover"
                  class="fms-search-item-thumbnail"
                  @error="handleImageError"
                >
                  <template #error>
                    <div class="fms-search-item-icon-fallback">
                      <el-icon class="fms-search-item-svg-icon">
                        <Picture />
                      </el-icon>
                    </div>
                  </template>
                </el-image>
                <!-- 非图片文件或图片加载失败时显示文件类型图标 -->
                <vab-icon
                  v-else
                  :icon="getFileTypeIcon(item)"
                  is-custom-svg
                  class="fms-search-item-svg-icon"
                />
              </div>
              <div class="fms-search-item-content">
                <span
                  class="fms-search-item-title"
                  v-html="item.highlighted_name"
                ></span>
                <div class="fms-search-item-info">
                  <el-tag size="small" :type="getTypeTagType(item.type)">
                    {{ item.type === 'file' ? '文件' : '目录' }}
                  </el-tag>
                  <span class="dot-divider">•</span>
                  <el-tag size="small" type="info">
                    {{ getVisibilityText(item.visibility) }}
                  </el-tag>
                  <span class="dot-divider">•</span>
                  <span>{{ formatDate(item.updated_at) }}</span>
                  <span v-if="item.size" class="dot-divider">•</span>
                  <span v-if="item.size">{{ formatFileSize(item.size) }}</span>
                </div>
                <!-- 创建人和编辑人信息 -->
                <div class="fms-search-item-users">
                  <div v-if="item.creator" class="fms-search-creator-info">
                    <span class="fms-search-user-label">创建人:</span>
                    <img
                      :src="item.creator.thumb_avatar || item.creator.avatar"
                      class="fms-search-creator-avatar"
                      @error="handleAvatarError"
                    />
                    <span class="fms-search-creator-name">
                      {{ item.creator.name }}
                    </span>
                  </div>
                  <div v-if="item.updater" class="fms-search-updater-info">
                    <span class="fms-search-user-label">编辑人:</span>
                    <img
                      :src="item.updater.thumb_avatar || item.updater.avatar"
                      class="fms-search-updater-avatar"
                      @error="handleAvatarError"
                    />
                    <span class="fms-search-updater-name">
                      {{ item.updater.name }}
                    </span>
                  </div>
                </div>
                <div class="fms-search-item-path">
                  <vab-icon
                    icon="home-7-fill"
                    :style="{
                      width: 16 + 'px',
                      height: 16 + 'px',
                    }"
                    class="path-icon"
                  />
                  <!-- <el-icon class="path-icon"><FolderOpened /></el-icon> -->
                  <span>{{ item.path_info?.path || '/' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="limit"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </firefly-dialog>
</template>

<script setup>
  import { ref, inject } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import {
    Search,
    FolderOpened,
    Document,
    Picture,
    VideoPlay,
    Files,
    Reading,
    Tickets,
    Box,
  } from '@element-plus/icons-vue'
  import { search as searchFiles } from '@/api/fms/files'
  import { parseTime, getFileType } from '@/utils/index'
  import { getUserThirdList } from '@/api/user'
  import PersonnelSelect from '@/components/PersonnelSelect.vue'

  const router = useRouter()
  const route = useRoute()
  const $baseMessage = inject('$baseMessage')

  // 响应式变量
  const visible = ref(false)
  const searchText = ref('')
  const typeFilter = ref('')
  const selectedCreators = ref([])
  const searchResult = ref([])
  const loading = ref(false)
  const page = ref(1)
  const limit = ref(20)
  const total = ref(0)
  const showThumbnail = ref(true) // 是否显示图片缩略图

  // 用户相关变量
  const userList = ref([])

  // 打开搜索弹窗
  const showSearch = (keyword) => {
    searchText.value = keyword || ''
    visible.value = true

    // 初始化用户列表
    if (userList.value.length === 0) {
      fetchUserList()
    }

    // 只有当有关键词时才执行搜索
    if (searchText.value.trim()) {
      fetchSearchResults()
    }
  }

  // 获取用户列表
  const fetchUserList = async () => {
    try {
      // 使用getUserThirdList获取用户列表
      const params = { third: 'redmine' }
      const res = await getUserThirdList(params)
      userList.value = res.data || []
    } catch (error) {
      console.error('获取用户列表失败:', error)
      userList.value = []
    }
  }

  // 获取搜索结果
  const fetchSearchResults = async () => {
    if (!searchText.value.trim()) {
      return
    }

    loading.value = true

    try {
      const params = {
        keywords: searchText.value,
        page: page.value,
        limit: limit.value,
        sort: 'updated_at',
        order: 'DESC',
        filter: {},
        op: {},
      }

      // 应用筛选条件
      if (typeFilter.value) {
        params.filter.type = typeFilter.value
      }

      if (selectedCreators.value && selectedCreators.value.length > 0) {
        params.filter.creator_ids = selectedCreators.value
      }

      const { data } = await searchFiles(params)

      if (data) {
        searchResult.value = data.data || []
        total.value = data.total || 0
      } else {
        searchResult.value = []
        total.value = 0
      }
    } catch (error) {
      console.error('搜索失败:', error)
      $baseMessage('搜索失败', 'error', 'vab-hey-message-error')
      searchResult.value = []
      total.value = 0
    } finally {
      loading.value = false
    }
  }

  // 格式化日期
  const formatDate = (date) => {
    if (!date) return ''
    return parseTime(date, '{y}-{m}-{d} {h}:{i}')
  }

  // 格式化文件大小
  const formatFileSize = (size) => {
    if (!size) return ''

    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let index = 0
    let fileSize = size

    while (fileSize >= 1024 && index < units.length - 1) {
      fileSize /= 1024
      index++
    }

    return `${fileSize.toFixed(index === 0 ? 0 : 1)} ${units[index]}`
  }

  // 获取可见性文本
  const getVisibilityText = (visibility) => {
    const map = {
      public: '公开',
      department: '部门',
      user: '用户',
      private: '私有',
    }
    return map[visibility] || visibility
  }

  // 获取类型标签类型
  const getTypeTagType = (type) => {
    return type === 'file' ? 'success' : 'primary'
  }

  // 获取文件图标样式类
  const getFileIconClass = (fileType) => {
    const classMap = {
      image: 'file-icon-image',
      video: 'file-icon-video',
      audio: 'file-icon-audio',
      text: 'file-icon-text',
      pdf: 'file-icon-pdf',
      word: 'file-icon-word',
      excel: 'file-icon-excel',
      powerpoint: 'file-icon-powerpoint',
      archive: 'file-icon-archive',
      unknown: 'file-icon-unknown',
    }
    return classMap[fileType] || 'file-icon-unknown'
  }

  // 创建人变化处理
  const handleCreatorChange = () => {
    fetchSearchResults()
  }

  // 分页处理
  const handleSizeChange = (val) => {
    limit.value = val
    fetchSearchResults()
  }

  const handleCurrentChange = (val) => {
    page.value = val
    fetchSearchResults()
  }

  // 打开项目
  const openItem = (item) => {
    let url = ''

    if (item.type === 'directory') {
      // 构建目录页面URL - 基于当前路由路径
      url = router.resolve({
        path: route.path,
        query: {
          ...route.query,
          directoryId: item.id,
        },
      }).href
    } else {
      // 构建文件预览页面URL - 基于当前路由路径
      const query = {
        ...route.query,
        fileId: item.id,
        action: 'preview',
      }

      // 处理文件所在目录ID
      if (item.directory_id && item.directory_id !== 0) {
        query.directoryId = item.directory_id
      }

      url = router.resolve({
        path: route.path,
        query: query,
      }).href
    }

    // 在新页面打开
    window.open(url, '_blank')
    // visible.value = false
  }

  // 关闭弹窗
  const handleClose = () => {
    visible.value = false
    searchResult.value = []
    page.value = 1
    typeFilter.value = ''
    selectedCreators.value = []
  }

  // 头像加载错误处理
  const handleAvatarError = (event) => {
    // 设置默认头像或隐藏图片
    event.target.style.display = 'none'
  }

  // 判断是否为图片文件
  const isImageFile = (item) => {
    if (!item.mime_type && !item.name) return false

    const mimeType = item.mime_type || ''
    const fileName = item.name || ''
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''

    return (
      mimeType.startsWith('image/') ||
      ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'].includes(
        fileExtension
      )
    )
  }

  // 获取缩略图URL
  const getThumbnailUrl = (item) => {
    return (
      item.preview_url ||
      item.previewUrl ||
      item.download_url ||
      item.downloadUrl ||
      item.url ||
      ''
    )
  }

  // 获取文件类型图标
  const getFileTypeIcon = (item) => {
    const mimeType = item.mime_type || ''
    const fileType = getFileType(mimeType)

    if (fileType === 'other') {
      return 'unknow_file'
    }
    return `icon-${fileType}`
  }

  // 图片加载错误处理
  const handleImageError = () => {
    // 图片加载失败时会自动显示fallback内容
    console.log('图片加载失败')
  }

  // 导出方法，供父组件调用
  defineExpose({
    showSearch,
  })
</script>

<style lang="scss" scoped>
  :deep() {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form--inline .el-form-item {
      margin-right: 10px;
    }
  }

  .fms-search-container {
    padding: 0 10px;
  }

  .fms-search-results {
    min-height: 600px;
    margin-top: 16px;
  }

  .result-list {
    min-height: 600px;
    max-height: 600px;
    margin-bottom: 16px;
    overflow-y: scroll;
  }

  .fms-search-item {
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    border-radius: 8px;
    border: none;
    transition: all 0.2s;

    &:hover {
      background-color: #f5f7fa;
      // border-color: #409eff;
      // box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      border: none;
    }
  }

  .fms-search-item-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
  }

  .fms-search-item-icon {
    flex-shrink: 0;
    margin-top: 2px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    .directory-icon {
      font-size: 20px;
      color: #409eff;
    }

    .fms-search-item-thumbnail {
      width: 20px;
      height: 20px;
      border-radius: 2px;
    }

    .fms-search-item-icon-fallback {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      border-radius: 2px;
    }

    .fms-search-item-svg-icon {
      width: 18px;
      height: 18px;
      min-width: 18px;
      max-height: 20px;
    }

    .file-icon {
      font-size: 18px;

      &.file-icon-image {
        color: #67c23a;
      }
      &.file-icon-video {
        color: #e6a23c;
      }
      &.file-icon-audio {
        color: #f56c6c;
      }
      &.file-icon-text {
        color: #909399;
      }
      &.file-icon-pdf {
        color: #f56c6c;
      }
      &.file-icon-word {
        color: #409eff;
      }
      &.file-icon-excel {
        color: #67c23a;
      }
      &.file-icon-powerpoint {
        color: #e6a23c;
      }
      &.file-icon-archive {
        color: #909399;
      }
      &.file-icon-unknown {
        color: #c0c4cc;
      }
    }
  }

  .fms-search-item-content {
    flex: 1;
    min-width: 0;
  }

  .fms-search-item-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    line-height: 1.4;
    display: block;
    margin-bottom: 8px;

    :deep(.highlight) {
      font-weight: bold;
      color: #f56c6c;
      background-color: #fef0f0;
      padding: 1px 3px;
      border-radius: 2px;
    }
  }

  .fms-search-item-info {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
  }

  .fms-search-item-path {
    font-size: 12px;
    color: #606266;
    display: flex;
    align-items: center;
    gap: 4px;

    .path-icon {
      font-size: 14px;
      color: #909399;
    }
  }

  /* 创建人和编辑人样式 */
  .fms-search-item-users {
    margin: 8px 0;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }

  .fms-search-creator-info,
  .fms-search-updater-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
  }

  .fms-search-user-label {
    color: #909399;
    font-weight: 500;
  }

  .fms-search-creator-avatar,
  .fms-search-updater-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #e4e7ed;
  }

  .fms-search-creator-name,
  .fms-search-updater-name {
    color: #606266;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dot-divider {
    margin: 0 6px;
    color: #dcdfe6;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-start;
    margin-top: 16px;
  }

  .no-results {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
  }

  /* 深度选择器，确保 vab-icon 正确显示 */
  :deep(.vab-icon) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
