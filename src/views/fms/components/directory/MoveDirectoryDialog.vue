<template>
  <firefly-dialog
    v-model="dialogVisible"
    title="移动目录"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="move-directory-dialog__content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="移动目录">
          <el-input :value="currentDirectoryName" readonly disabled>
            <template #prefix>
              <vab-icon
                :icon="'icon-dir'"
                is-custom-svg
                :style="{
                  width: 16 + 'px',
                  height: 16 + 'px',
                  marginRight: '6px',
                }"
              />
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="当前位置">
          <el-input :value="currentPath" readonly disabled>
            <template #prefix>
              <el-icon><Location /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="目标位置" prop="targetParentId">
          <div class="move-directory-dialog__tree-container">
            <div class="move-directory-dialog__tree-header">
              <span>选择目标目录：</span>
              <el-button
                type="text"
                :icon="Refresh"
                size="small"
                @click="refreshTree"
              >
                刷新
              </el-button>
            </div>

            <div class="move-directory-dialog__tree-content">
              <a-tree
                v-if="showTree && treeData.length > 0"
                ref="treeRef"
                :tree-data="treeData"
                :height="300"
                :field-names="fieldNames"
                :selected-keys="selectedKeys"
                :expanded-keys="expandedKeys"
                :disabled-keys="disabledKeys"
                show-line
                block-node
                @select="handleTreeSelect"
                @expand="handleTreeExpand"
              >
                <template #title="{ title, disabled }">
                  <div class="move-directory-dialog__tree-node">
                    <el-icon class="move-directory-dialog__tree-icon">
                      <Folder />
                    </el-icon>
                    <span
                      class="move-directory-dialog__tree-title"
                      :class="{ 'is-disabled': disabled }"
                    >
                      {{ title }}
                    </span>
                  </div>
                </template>
              </a-tree>

              <div
                v-else-if="isTreeLoading"
                class="move-directory-dialog__tree-loading"
              >
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>加载目录树...</span>
              </div>

              <div v-else class="move-directory-dialog__tree-empty">
                <el-empty description="暂无可选择的目录" />
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="新位置">
          <el-input :value="targetPath" readonly placeholder="请选择目标目录">
            <template #prefix>
              <el-icon><FolderOpened /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>

      <div class="move-directory-dialog__tips">
        <el-alert type="warning" :closable="false" show-icon>
          <template #title>
            <div class="move-directory-dialog__tips-content">
              <div>移动注意事项：</div>
              <ul>
                <li>不能移动到自身或其子目录中</li>
                <li>移动后可能影响已分享的链接</li>
                <li>移动大目录可能需要较长时间</li>
                <li>请确保对目标位置有写入权限</li>
              </ul>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="move-directory-dialog__footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="isSubmitting"
          :disabled="!canMove"
          @click="handleSubmit"
        >
          确认移动
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    Folder,
    FolderOpened,
    Location,
    Refresh,
    Loading,
  } from '@element-plus/icons-vue'
  import { useDirectoryStore } from '../../stores/directoryStore'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      default: null,
    },
    targetParentId: {
      type: [Number, String],
      default: null,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'success'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const directoryStore = useDirectoryStore()

  // 响应式数据
  const formRef = ref()
  const treeRef = ref()
  const isSubmitting = ref(false)
  const isTreeLoading = ref(false)
  const showTree = ref(true)
  const treeData = ref([])
  const selectedKeys = ref([])
  const expandedKeys = ref([])
  const disabledKeys = ref([])

  const formData = reactive({
    targetParentId: null,
  })

  // 表单验证规则
  const formRules = {
    targetParentId: [
      { required: true, message: '请选择目标目录', trigger: 'change' },
    ],
  }

  // 字段映射配置
  const fieldNames = {
    key: 'id',
    title: 'name',
    children: 'children',
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  const currentDirectoryName = computed(() => {
    if (!props.id) return ''
    const directory = directoryStore.directoryMap.get(props.id)
    return directory ? directory.name : '未知目录'
  })

  const currentPath = computed(() => {
    if (!props.id) return '根目录'
    const directory = directoryStore.directoryMap.get(props.id)
    return directory ? directory.path : '未知路径'
  })

  const targetPath = computed(() => {
    if (!formData.targetParentId) return '根目录'
    const directory = directoryStore.directoryMap.get(formData.targetParentId)
    return directory ? directory.path : '未知路径'
  })

  const canMove = computed(() => {
    return (
      formData.targetParentId !== null &&
      formData.targetParentId !== props.targetParentId
    )
  })

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        resetForm()
        loadTreeData()
      }
    }
  )

  // 方法
  const resetForm = () => {
    formData.targetParentId = props.targetParentId
    selectedKeys.value = props.targetParentId
      ? [String(props.targetParentId)]
      : []

    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  const loadTreeData = async () => {
    try {
      isTreeLoading.value = true
      showTree.value = false

      const response = await directoryStore.fetchDirectoryTree({
        includeRoot: true,
        includePermissions: true,
      })

      // 构建树数据并设置禁用状态
      treeData.value = buildTreeData(response.tree)

      // 设置初始展开
      if (treeData.value.length > 0) {
        expandedKeys.value = [String(treeData.value[0].id)]
      }

      showTree.value = true
    } catch (error) {
      $baseMessage('加载目录树失败', 'error', 'vab-hey-message-error')
    } finally {
      isTreeLoading.value = false
    }
  }

  const buildTreeData = (nodes) => {
    return nodes.map((node) => {
      const isDisabled = isNodeDisabled(node)

      if (isDisabled) {
        disabledKeys.value.push(String(node.id))
      }

      return {
        ...node,
        key: String(node.id),
        title: node.name,
        disabled: isDisabled,
        children: node.children ? buildTreeData(node.children) : undefined,
      }
    })
  }

  const isNodeDisabled = (node) => {
    // 不能移动到自身
    if (node.id === props.id) {
      return true
    }

    // 不能移动到自身的子目录（需要检查路径）
    if (
      currentPath.value &&
      node.path &&
      node.path.startsWith(currentPath.value + '/')
    ) {
      return true
    }

    // 检查权限（需要有管理权限才能移动到该目录）
    if (node.permissions && !(node.permissions & 16)) {
      // 16 = MANAGE
      return true
    }

    return false
  }

  const refreshTree = () => {
    loadTreeData()
  }

  const handleTreeSelect = (selectedKeys, { node }) => {
    if (selectedKeys.length > 0 && !node.disabled) {
      formData.targetParentId = Number(node.key)
      selectedKeys.value = [node.key]
    }
  }

  const handleTreeExpand = (expandedKeys) => {
    expandedKeys.value = expandedKeys
  }

  const handleSubmit = async () => {
    if (!formRef.value || !canMove.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      isSubmitting.value = true

      await directoryStore.moveDirectory(props.id, formData.targetParentId)

      $baseMessage('目录移动成功', 'success', 'vab-hey-message-success')

      emit('success', {
        id: props.id,
        oldParentId: props.targetParentId,
        newParentId: formData.targetParentId,
      })

      handleClose()
    } catch (error) {
      $baseMessage(
        error.message || '目录移动失败',
        'error',
        'vab-hey-message-error'
      )
    } finally {
      isSubmitting.value = false
    }
  }

  const handleClose = () => {
    dialogVisible.value = false
  }
</script>

<style lang="scss" scoped>
  .move-directory-dialog {
    &__content {
      max-height: 600px;
      overflow-y: auto;
    }

    &__tree-container {
      border: 1px solid var(--el-border-color-light);
      border-radius: 6px;
      overflow: hidden;
    }

    &__tree-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background: var(--el-bg-color-page);
      border-bottom: 1px solid var(--el-border-color-lighter);
      font-size: 14px;
      font-weight: 500;
    }

    &__tree-content {
      padding: 8px;
      max-height: 300px;
      overflow-y: auto;
    }

    &__tree-loading,
    &__tree-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: var(--el-text-color-placeholder);

      .el-icon {
        margin-right: 8px;
      }
    }

    &__tree-node {
      display: flex;
      align-items: center;
      gap: 6px;
      width: 100%;
    }

    &__tree-icon {
      font-size: 14px;
      color: var(--el-color-primary);
    }

    &__tree-title {
      font-size: 13px;
      color: var(--el-text-color-primary);

      &.is-disabled {
        color: var(--el-text-color-disabled);
      }
    }

    &__tips {
      margin-top: 16px;

      &-content {
        font-size: 13px;
        line-height: 1.5;

        ul {
          margin: 8px 0 0 0;
          padding-left: 16px;

          li {
            margin-bottom: 4px;
            color: var(--el-text-color-regular);
          }
        }
      }
    }

    &__footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: var(--el-fill-color-light);
  }

  // antd-vue 树组件样式覆盖
  :deep(.ant-tree) {
    background: transparent;

    .ant-tree-node-content-wrapper {
      padding: 2px 4px;
      border-radius: 4px;

      &:hover {
        background-color: var(--el-color-primary-light-9);
      }

      &.ant-tree-node-selected {
        background-color: var(--el-color-primary-light-8);
      }

      &.ant-tree-node-disabled {
        color: var(--el-text-color-disabled);
        cursor: not-allowed;

        &:hover {
          background-color: transparent;
        }
      }
    }

    .ant-tree-title {
      width: 100%;
    }

    .ant-tree-switcher {
      color: var(--el-text-color-regular);

      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }
  }
</style>
