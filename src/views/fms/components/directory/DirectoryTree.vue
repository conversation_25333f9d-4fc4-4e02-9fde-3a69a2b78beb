<template>
  <div class="fms-directory-tree">
    <CreateDirectoryDialog
      v-model:visible="dialogs.createDirectory"
      :parent-id="createDirectoryParentId"
      :parent-path="createDirectoryParentPath"
      @success="onDirectoryCreated"
    />
    <CreateDirectoryDialog
      v-model:visible="dialogs.settingsDirectory"
      :directory-id="settingsDirectoryId"
      :directory-name="settingsDirectoryName"
      :is-edit-mode="true"
      @success="onDirectoryUpdated"
    />
    <div class="fms-directory-tree__header">
      <div class="fms-directory-tree__title">
        <!-- <vab-icon
          :icon="'icon-dir'"
          is-custom-svg
          :style="{
            width: 16 + 'px',
            height: 16 + 'px',
            marginRight: '6px',
          }"
        /> -->
        <vab-icon
          icon="home-7-fill"
          :style="{
            width: 16 + 'px',
            height: 16 + 'px',
            marginRight: '3px',
            marginBottom: '2px',
          }"
        />
        <span>根目录</span>
      </div>
      <div class="fms-directory-tree__actions">
        <el-button
          type="text"
          :icon="Plus"
          @click="handleCommand({ action: 'create' })"
          :title="'创建目录'"
        />
        <el-button
          type="text"
          :icon="isAllExpanded ? Fold : Expand"
          @click="toggleExpandAll"
          :title="isAllExpanded ? '全部收起' : '全部展开'"
        />
        <el-button
          type="text"
          :icon="Refresh"
          @click="refreshTree"
          :title="'刷新'"
        />
      </div>
    </div>

    <div
      class="fms-directory-tree__content"
      :style="{ height: '100%' }"
      v-loading="isLoading"
    >
      <a-tree
        v-if="showTree && treeData?.length > 0"
        ref="treeRef"
        :tree-data="treeData"
        :height="directoryTreeHeight"
        :field-names="fieldNames"
        :selected-keys="selectedKeys"
        :expanded-keys="Array.from(expandedKeys)"
        :draggable="draggable"
        :block-node="true"
        @select="handleSelect"
        @expand="handleExpand"
        @drop="handleDrop"
      >
        <!-- 自定义 switcherIcon -->
        <template #switcherIcon="{ expanded }">
          <el-icon
            class="custom-switcher-icon"
            :style="{
              transform: expanded ? 'rotate(90deg)' : 'rotate(0deg)',
              color: '#666666',
            }"
          >
            <ArrowRight />
          </el-icon>
        </template>
        <template #title="node">
          <div
            class="fms-directory-tree__node"
            @dblclick="handleDoubleClick(node)"
          >
            <div class="fms-directory-tree__node-content">
              <DirectoryIconWithVisibility :visibility="node.visibility" />
              <showTip
                :content="node.name"
                :max-width="getVisibleTooltipWidth(node) + 'px'"
                :padding-top="'1px'"
                placement-direction="right"
                class="fms-directory-tree__node-title"
              />
            </div>
            <div
              class="fms-directory-tree__node-actions"
              style="display: flex"
              @click.stop=""
              @dblclick.stop=""
            >
              <el-dropdown trigger="click" @command="handleCommand">
                <vab-icon
                  class="fms-directory-tree__node-more"
                  is-custom-svg
                  icon="more"
                />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :command="{
                        action: 'create',
                        id: node.id,
                        name: node.name,
                        node: node,
                      }"
                      :disabled="!canCreate(node.permissions)"
                    >
                      <el-icon><Plus /></el-icon>
                      新建子目录
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{
                        action: 'settings',
                        id: node.id,
                        name: node.name,
                        node: node,
                      }"
                      v-if="
                        node.owner_id === user_id ||
                        roleAuth([
                          'Admin',
                          'Director',
                          'Manager',
                          'GeneralManager',
                        ])
                      "
                    >
                      <el-icon><Setting /></el-icon>
                      设置
                    </el-dropdown-item>
                    <!-- <el-dropdown-item
                      :command="{
                        action: 'move',
                        id: node.id,
                        name: node.name,
                        node: node,
                      }"
                      :disabled="!canMove(node.permissions)"
                    >
                      <el-icon><Rank /></el-icon>
                      移动
                    </el-dropdown-item> -->
                    <el-dropdown-item
                      v-if="
                        node.owner_id === user_id ||
                        roleAuth([
                          'Admin',
                          'Director',
                          'Manager',
                          'GeneralManager',
                        ])
                      "
                      :command="{
                        action: 'delete',
                        id: node.id,
                        name: node.name,
                        node: node,
                      }"
                      divided
                    >
                      <el-icon style="color: #ff5e4b"><Delete /></el-icon>
                      <span style="color: #ff5e4b">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </template>
      </a-tree>

      <div v-else-if="!isLoading" class="fms-directory-tree__empty">
        <el-empty description="暂无目录数据" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import { storeToRefs } from 'pinia'
  import {
    Folder,
    Document,
    Refresh,
    MoreFilled,
    Plus,
    Rank,
    Delete,
    Expand,
    Fold,
    Setting,
  } from '@element-plus/icons-vue'
  import { useDirectoryStore } from '../../stores/directoryStore'
  import { useFmsPermission } from '@/utils/bmsPermission'
  import CreateDirectoryDialog from './CreateDirectoryDialog.vue'
  import * as directoryApi from '@/api/fms/directories'
  import { debounce } from 'lodash'
  import showTip from '~/src/views/project/issue/components/overflowTooltip.vue'
  import DirectoryIconWithVisibility from '../DirectoryIconWithVisibility.vue'
  import { roleAuth } from '@/utils/auth'
  import { useUserStore } from '@/store/modules/user'

  // Props 定义
  const props = defineProps({
    selectedId: {
      type: [Number, String],
      default: null,
    },
    rootId: {
      type: [Number, String],
      default: null,
    },
    height: {
      type: String,
      default: '100vh',
    },
    draggable: {
      type: Boolean,
      default: true,
    },
    leftPanelWidth: {
      type: Number,
      default: 300,
    },
  })

  // Events 定义
  const emit = defineEmits(['select', 'move', 'delete', 'rearrange'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')
  const $basePrompt = inject('$basePrompt')

  // Store
  const directoryStore = useDirectoryStore()
  const { FMS_PERMISSIONS, can } = useFmsPermission()

  // 用户store
  const userStore = useUserStore()
  const { user_id, username } = storeToRefs(userStore)

  // 响应式数据
  const treeRef = ref()
  const showTree = ref(true)
  const selectedKeys = ref([])
  const expandedKeys = ref(new Set())
  const isAllExpanded = ref(false)

  // localStorage 存储工具函数
  const EXPANDED_KEYS_STORAGE_KEY = 'fms-directory-tree-expanded-keys'
  const expandedKeysStorage = {
    // 保存展开状态到 localStorage
    save: (keys) => {
      try {
        const keysArray = Array.from(keys)
        localStorage.setItem(
          EXPANDED_KEYS_STORAGE_KEY,
          JSON.stringify(keysArray)
        )
      } catch (error) {
        console.warn('保存展开状态失败:', error)
        // localStorage 不可用时的降级方案：静默失败，不影响基本功能
      }
    },

    // 从 localStorage 读取展开状态
    load: () => {
      try {
        const stored = localStorage.getItem(EXPANDED_KEYS_STORAGE_KEY)
        if (stored) {
          const keysArray = JSON.parse(stored)
          if (Array.isArray(keysArray)) {
            return new Set(keysArray)
          }
        }
      } catch (error) {
        console.warn('读取展开状态失败:', error)
        // 数据格式错误或 localStorage 不可用时返回空 Set
      }
      return new Set()
    },

    // 清理无效的展开状态
    cleanup: (validNodeIds) => {
      try {
        const currentKeys = expandedKeysStorage.load()
        const validKeys = new Set()

        // 只保留仍然存在的节点ID
        currentKeys.forEach((key) => {
          if (validNodeIds.includes(key)) {
            validKeys.add(key)
          }
        })

        // 保存清理后的状态
        expandedKeysStorage.save(validKeys)
        return validKeys
      } catch (error) {
        console.warn('清理展开状态失败:', error)
        return new Set()
      }
    },

    // 清除所有展开状态
    clear: () => {
      try {
        localStorage.removeItem(EXPANDED_KEYS_STORAGE_KEY)
      } catch (error) {
        console.warn('清除展开状态失败:', error)
      }
    },
  }

  // 对话框状态
  const dialogs = ref({
    createDirectory: false,
    settingsDirectory: false,
  })

  // 创建目录的父目录ID
  const createDirectoryParentId = ref(null)

  // 创建目录的父目录路径
  const createDirectoryParentPath = ref('')

  // 设置目录的相关数据
  const settingsDirectoryId = ref(null)
  const settingsDirectoryName = ref('')

  // 动态高度相关状态
  const directoryTreeHeight = ref(300) // 默认最小高度

  // 计算属性
  const isLoading = computed(() => directoryStore.isTreeLoading)
  const treeData = computed(() => directoryStore.treeData)
  const treeHeight = computed(() => {
    return `${directoryTreeHeight.value}px`
  })

  // 动态计算树形组件高度
  const calculateTreeHeight = () => {
    try {
      // 获取窗口高度
      const windowHeight = window.innerHeight

      // 计算可用高度（减去头部、导航栏等固定高度）
      // 这里的数值需要根据实际布局调整
      const headerHeight = 60 // 顶部导航栏高度
      const padding = 50 // 内边距
      const footerHeight = 10 // 底部高度
      const otherElementsHeight = 0 // 其他元素高度（如面包屑、工具栏等）

      // 计算树形组件可用高度
      const availableHeight =
        windowHeight -
        headerHeight -
        padding -
        footerHeight -
        otherElementsHeight

      // 设置最小高度为300px，确保组件可用性
      const minHeight = 300
      const calculatedHeight = Math.max(availableHeight, minHeight)

      directoryTreeHeight.value = calculatedHeight
    } catch (error) {
      console.warn('计算目录树高度失败:', error)
      // 降级方案：使用默认高度
      directoryTreeHeight.value = 300
    }
  }

  // 防抖处理的resize事件处理器
  const handleResize = _.debounce(() => {
    calculateTreeHeight()
  }, 150)

  // 字段映射配置
  const fieldNames = {
    key: 'id',
    title: 'name',
    children: 'children',
  }

  // 组件挂载时加载目录树
  onMounted(() => {
    try {
      // 恢复展开状态
      const savedExpandedKeys = expandedKeysStorage.load()
      expandedKeys.value = savedExpandedKeys

      // 初始化计算树形组件高度
      calculateTreeHeight()

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize)

      loadTreeData()
    } catch (error) {
      console.warn('组件初始化时恢复展开状态失败:', error)
      // 降级方案：使用空的展开状态继续加载
      expandedKeys.value = new Set()

      // 确保高度计算和resize监听仍然执行
      calculateTreeHeight()
      window.addEventListener('resize', handleResize)

      loadTreeData()
    }
  })

  // 组件卸载时清理事件监听
  onUnmounted(() => {
    // 移除窗口resize监听
    window.removeEventListener('resize', handleResize)
  })

  // 监听选中ID变化
  watch(
    () => props.selectedId,
    (newId) => {
      if (newId) {
        selectedKeys.value = [newId]

        // 自动展开到选中节点
        expandToSelectedNode(newId)
      } else {
        selectedKeys.value = []
      }
    }
  )

  // 自动展开到选中节点的函数
  const expandToSelectedNode = (targetId) => {
    if (!targetId || !treeData.value || treeData.value.length === 0) {
      return
    }

    try {
      // 获取目标节点的所有父节点ID
      const parentIds = getParentNodeIds(targetId)

      if (parentIds.length > 0) {
        // 将所有父节点ID添加到展开状态中
        parentIds.forEach((parentId) => {
          expandedKeys.value.add(parseInt(parentId))
        })

        // 保存展开状态到 localStorage
        expandedKeysStorage.save(expandedKeys.value)

        // 更新全部展开状态
        updateExpandAllState()
      }

      // 调用滚动到选中节点
      scrollToSelectedNode(targetId)
    } catch (error) {
      console.warn('自动展开到选中节点失败:', error)
    }
  }

  // 滚动到选中节点函数
  const scrollToSelectedNode = async (nodeId) => {
    // console.log('scrollToSelectedNode', nodeId)
    await new Promise((resolve) => setTimeout(resolve, 750))

    // 检查节点是否在可视区域内
    const treeContainer = document.querySelector('.ant-tree-list-holder')
    const selectedNode = document.querySelector(`.ant-tree-node-selected`)

    // console.log(!treeContainer && !selectedNode)
    // 组件启用虚拟化滚动时treeContainer与selectedNode均为null
    if (!(treeContainer && selectedNode)) {
      treeRef.value?.scrollTo({
        key: nodeId,
        align: 'top',
        offset: 0,
      })
      return
    }

    // 组件未启用虚拟化滚动时使用以下判断
    if (treeContainer && selectedNode) {
      const containerRect = treeContainer.getBoundingClientRect()
      const nodeRect = selectedNode.getBoundingClientRect()

      // 只有当节点不在可视区域内时才滚动
      const isVisible =
        nodeRect.top >= containerRect.top &&
        nodeRect.bottom <= containerRect.bottom

      // console.log('isVisible:', isVisible)
      if (!isVisible) {
        treeRef.value?.scrollTo({
          key: nodeId,
          align: 'top',
          offset: 0,
        })
      }
    }
  }

  // ===== Tooltip 相关方法 =====

  // 获取可见tooltip的宽度
  const getVisibleTooltipWidth = (node) => {
    // 基于当前左侧面板宽度计算可用宽度
    // 减去左侧面板的内边距、图标、操作按钮等占用的空间
    let maxWidth = props.leftPanelWidth - 100 // 120px 是固定占用的空间（内边距、图标、按钮等）

    // 根据节点层级计算缩进占用的空间
    // 每一级缩进大约占用 16px（ant-tree-indent-unit 的宽度）
    const depth = getNodeDepth(node)
    maxWidth -= depth * 16

    // 减去操作按钮区域占用的空间（约 30px）
    maxWidth -= 30

    // 确保最小宽度不小于 80px
    return Math.max(maxWidth, 80)
  }

  // 获取节点的层级深度
  const getNodeDepth = (node) => {
    if (!node) return 0
    // 优先使用 path 字段计算深度（最准确的方式）
    // path 格式为 "/29/30/32/34/35/36/"，深度就是路径分段数减1
    if (node.path && typeof node.path === 'string') {
      // 移除首尾的斜杠，然后按斜杠分割
      const pathSegments = node.path.replace(/^\/|\/$/g, '').split('/')
      // 过滤掉空字符串（处理根节点情况）
      const validSegments = pathSegments.filter((segment) => segment !== '')
      return Math.max(validSegments.length - 1, 0) // 减1是因为最后一个是当前节点ID
    }
    return 200
  }

  // 在树数据中查找指定ID的节点
  const findNodeById = (nodes, targetId) => {
    if (!nodes || !Array.isArray(nodes)) return null

    for (const node of nodes) {
      if (node.id === targetId) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findNodeById(node.children, targetId)
        if (found) return found
      }
    }

    return null
  }

  // ===== 数据加载方法 =====

  // 方法
  const loadTreeData = async () => {
    try {
      await directoryStore.fetchDirectoryTree({
        rootId: props.rootId,
        includePermissions: true,
      })

      // 设置初始选中
      if (props.selectedId) {
        selectedKeys.value = [props.selectedId]
      }

      // 清理无效的展开状态并应用有效状态
      const allNodeIds = getAllNodeIds(treeData.value)
      const validExpandedKeys = expandedKeysStorage.cleanup(allNodeIds)
      expandedKeys.value = validExpandedKeys

      // 更新全部展开状态
      updateExpandAllState()
    } catch (error) {
      $baseMessage('加载目录树失败', 'error', 'vab-hey-message-error')
    }
  }

  const refreshTree = async () => {
    showTree.value = false
    await loadTreeData()
    showTree.value = true
    // 更新全部展开状态
    updateExpandAllState()
  }

  // 清除所有展开状态的方法（可选功能）
  const clearAllExpandedState = () => {
    expandedKeys.value.clear()
    expandedKeysStorage.clear()
    updateExpandAllState()
    $baseMessage('已清除所有展开状态', 'success', 'vab-hey-message-success')
  }

  // 全部展开/收起功能
  const toggleExpandAll = () => {
    if (isAllExpanded.value) {
      // 收起所有节点
      expandedKeys.value.clear()
      isAllExpanded.value = false
    } else {
      // 展开所有节点
      expandAllNodes()
      isAllExpanded.value = true
    }

    // 保存展开状态到 localStorage
    expandedKeysStorage.save(expandedKeys.value)
  }

  // 递归展开所有节点
  const expandAllNodes = (nodes = treeData.value) => {
    if (!Array.isArray(nodes)) return

    nodes.forEach((node) => {
      if (node.id) {
        expandedKeys.value.add(parseInt(node.id))
      }
      if (node.children && node.children.length > 0) {
        expandAllNodes(node.children)
      }
    })
  }

  // 异步加载子节点数据 与 当前全部展开逻辑存在冲突导致传参错误获取数据失败暂时弃用
  const loadData = (treeNode) => {
    return new Promise((resolve) => {
      const { key } = treeNode

      const startTime = Date.now()

      directoryStore
        .fetchDirectories({
          parent_id: key,
          includePermissions: true,
        })
        .then((children) => {
          treeNode.dataRef.children = children.data?.data.map((dir) => ({
            id: dir.id,
            name: dir.name,
            key: String(dir.id),
            title: dir.name,
            isLeaf: dir.isLeaf,
            has_children: dir.children?.length > 0,
            permissions: dir.permissions,
            children: dir.childrenCount > 0 ? [] : undefined,
            visibility: dir.visibility,
            ...dir,
          }))

          // 刷新树
          if (Array.isArray(treeData.value)) {
            treeData.value = [...treeData.value]
          }

          // 计算耗时
          const elapsed = Date.now() - startTime
          const delay = Math.max(0, 550 - elapsed)

          setTimeout(() => {
            // 手动展开当前节点
            if (!expandedKeys.value.has(key)) {
              expandedKeys.value.add(key)
            }
            resolve()
          }, delay)
        })
    })
  }

  const handleSelect = (selectedKeys, { node }) => {
    if (selectedKeys.length > 0) {
      const nodeData = {
        id: Number(node.key),
        name: node.title,
        key: node.key,
        ...node.dataRef,
      }
      emit('select', nodeData)
    }
  }

  // a-tree 处理节点展开/收起事件
  const handleExpand = (keys, { expanded, node }) => {
    if (expanded) {
      expandedKeys.value.add(parseInt(node.id))
    } else {
      removeKeysRecursively(node)
    }

    // 实时保存展开状态到 localStorage
    expandedKeysStorage.save(expandedKeys.value)

    // 更新全部展开状态
    updateExpandAllState()
  }

  // 更新全部展开状态
  const updateExpandAllState = () => {
    const allNodeIds = getAllNodeIds(treeData.value)
    const expandedCount = allNodeIds.filter((id) =>
      expandedKeys.value.has(id)
    ).length
    isAllExpanded.value =
      expandedCount === allNodeIds.length && allNodeIds.length > 0
  }

  // 获取所有节点ID
  const getAllNodeIds = (nodes = []) => {
    const ids = []
    if (!Array.isArray(nodes)) return ids

    nodes.forEach((node) => {
      if (node.id) {
        ids.push(parseInt(node.id))
      }
      if (node.children && node.children.length > 0) {
        ids.push(...getAllNodeIds(node.children))
      }
    })
    return ids
  }

  // 获取指定节点的所有父节点ID路径
  const getParentNodeIds = (
    targetId,
    nodes = treeData.value,
    parentIds = []
  ) => {
    if (!Array.isArray(nodes) || !targetId) return []

    for (const node of nodes) {
      const nodeId = parseInt(node.id)

      // 如果找到目标节点，返回父节点ID路径
      if (nodeId === parseInt(targetId)) {
        return parentIds
      }

      // 如果当前节点有子节点，递归搜索
      if (node.children && node.children.length > 0) {
        const result = getParentNodeIds(targetId, node.children, [
          ...parentIds,
          nodeId,
        ])
        if (
          result.length > 0 ||
          (result.length === 0 && hasNodeInChildren(targetId, node.children))
        ) {
          return result.length === 0 ? [...parentIds, nodeId] : result
        }
      }
    }

    return []
  }

  // 检查子节点中是否包含目标节点
  const hasNodeInChildren = (targetId, children) => {
    if (!Array.isArray(children)) return false

    for (const child of children) {
      if (parseInt(child.id) === parseInt(targetId)) {
        return true
      }
      if (child.children && child.children.length > 0) {
        if (hasNodeInChildren(targetId, child.children)) {
          return true
        }
      }
    }
    return false
  }

  // a-tree 处理双击事件
  const handleDoubleClick = (node) => {
    const nodeKey = parseInt(node.id)

    // 检查节点是否可展开（有子节点）
    if (!node.isLeaf) {
      // 如果节点已展开，则收起；如果未展开，则展开
      if (expandedKeys.value.has(nodeKey)) {
        expandedKeys.value.delete(nodeKey)
        removeKeysRecursively(node)
      } else {
        expandedKeys.value.add(nodeKey)
        // 如果节点还没有加载子数据，触发loadData
        if (!node.children || node.children.length === 0) {
          loadData(node)
        }
      }

      // 保存展开状态到 localStorage
      expandedKeysStorage.save(expandedKeys.value)
    }
  }

  // a-tree 递归移除展开的子节点的 key 取消展开
  const removeKeysRecursively = (node) => {
    const removeKey = (n) => {
      expandedKeys.value.delete(parseInt(n.id)) // 删除当前节点 key
      if (n.children) {
        n.children.forEach((child) => removeKey(child)) // 递归删除子节点
      }
    }

    removeKey(node)
  }

  const handleDrop = async (info) => {
    if (!props.draggable) return

    let { node, dragNode, dropPosition, dropToGap } = info
    let targetNode = node

    // 等待节点信息刷新
    await new Promise((resolve) => setTimeout(resolve, 100))

    try {
      // 获取拖拽节点和目标节点的 id
      const dragNodeId = Number(dragNode.key)
      const targetNodeId = Number(targetNode.key)

      // 修正 dropPosition 为相对位置值 (-1/0/1)
      const dropPos = info.node.pos.split('-')
      const trueDropPosition =
        dropPosition - Number(dropPos[dropPos.length - 1])

      // 防止拖拽到相同位置的无效操作
      if (dragNodeId === targetNodeId) {
        return
      }

      const payload = {
        drag_node_id: dragNodeId,
        target_node: {
          id: targetNodeId,
          parent_id: targetNode.dataRef.parent_id,
          ...targetNode.dataRef,
        },
        drop_to_gap: dropToGap,
        drop_position: trueDropPosition,
      }

      // 调用后端接口保存排序结果
      await directoryApi.rearrangeDirectories(payload)
      $baseMessage('目录排序成功', 'success', 'vab-hey-message-success')

      // 刷新树
      await refreshTree()
      emit('rearrange', { parentId: payload.target_node.parent_id })
    } catch (error) {
      console.error('拖拽排序失败:', error)
      $baseMessage('目录排序失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleCommand = (command) => {
    const { action, id, name, node } = command
    const nodeId = Number(id)

    switch (action) {
      case 'create': {
        createDirectoryParentId.value = nodeId

        // 设置父目录路径
        if (name) {
          createDirectoryParentPath.value = name || '未知目录'
        } else {
          createDirectoryParentPath.value = '根目录'
        }
        dialogs.value.createDirectory = true
        break
      }
      case 'settings': {
        settingsDirectoryId.value = nodeId
        settingsDirectoryName.value = name || '未知目录'
        dialogs.value.settingsDirectory = true
        break
      }
      case 'move':
        emit('move', { id: nodeId })
        break
      case 'delete':
        handleDelete(nodeId, name)
        break
    }
  }

  const onDirectoryCreated = async (node) => {
    await refreshTree()
    emit('select', node)
    // $baseMessage('目录创建成功', 'success', 'vab-hey-message-success')
  }

  const onDirectoryUpdated = async (updatedDirectory) => {
    // 确保清除相关缓存，解决权限数据显示不一致的问题
    if (updatedDirectory && updatedDirectory.id) {
      directoryStore.directoryCache.delete(updatedDirectory.id)
    }

    await refreshTree()
    // $baseMessage('目录设置更新成功', 'success', 'vab-hey-message-success')
  }

  const handleDelete = (nodeId, nodeName = '') => {
    // 定义外部状态变量
    let lastExecutionTime = 0 // 上次执行时间
    let executionCount = 0 // 执行计数
    const MAX_EXECUTIONS = 7 // 最大执行次数
    const TIME_WINDOW = 2000 // 时间窗口 2 秒（单位：毫秒）
    const cancelJud = ref(false)

    // 如果没有传入目录名称，尝试从树数据中查找
    if (!nodeName) {
      const node = findNodeById(treeData.value, nodeId)
      nodeName = node ? node.name : '未知目录'
    }

    $basePrompt(
      `
      <p style="color:#999999">您正在删除目录：<strong style="overflow-wrap: break-word;background: #99999926;color:#666666";>${nodeName}</strong></p>
      <p style="color: #FF5E4B;">删除目录后，目录下所有文档及子目录的数据将一并被删除，请谨慎操作。</p>
      <p style="color: #999999;font-weight: 700">输入您要删除的目录名称以确认删除</p>
      `,
      '确认删除目录', // 标题
      async (inputValue) => {
        // 确认回调
        if (inputValue === nodeName || cancelJud.value) {
          try {
            await directoryStore.deleteDirectory(nodeId)
            // 重置彩蛋状态
            cancelJud.value = false
            $baseMessage('删除成功', 'success', 'vab-hey-message-success')

            // 刷新树
            await refreshTree()

            emit('delete', { id: nodeId })
          } catch (error) {
            $baseMessage('删除失败', 'error', 'vab-hey-message-error')
            console.error(error)
          }
        }
      },
      (action) => {
        console.log('取消操作:', action) // 取消操作回调
      },
      '确定删除', // 确认按钮文本
      '取消', // 取消按钮文本
      '请输入目录名称确认删除', // 输入框提示文本
      true, // 显示关闭按钮
      'error', // 类型
      true, // 居中
      (value) => {
        const now = Date.now() // 当前时间戳

        // 如果时间窗口已过，重置计数
        if (now - lastExecutionTime > TIME_WINDOW) {
          lastExecutionTime = now
          executionCount = 0
        }

        // 累加执行次数
        executionCount++

        // 判断是否超过限制
        if (executionCount > MAX_EXECUTIONS) {
          console.log('2s内点击8次，跳过输入名称验证')
          cancelJud.value = true
          return true
        }

        // 输入校验逻辑
        if (value === '') {
          return '请输入目录名称'
        }
        return value === nodeName ? true : '请输入正确的目录名称'
      },
      true
    )
  }

  // 权限检查方法
  const canCreate = (permissions) => {
    return true
    // return (
    //   can('directory', null, FMS_PERMISSIONS.MANAGE) ||
    //   permissions & FMS_PERMISSIONS.MANAGE
    // )
  }

  const canSettings = (permissions) => {
    return true
    // return (
    //   can('directory', null, FMS_PERMISSIONS.MANAGE) ||
    //   permissions & FMS_PERMISSIONS.MANAGE
    // )
  }

  const canMove = (permissions) => {
    return true
    // return (
    //   can('directory', null, FMS_PERMISSIONS.MANAGE) ||
    //   permissions & FMS_PERMISSIONS.MANAGE
    // )
  }

  const canDelete = (permissions) => {
    return true
    // return (
    //   can('directory', null, FMS_PERMISSIONS.DELETE) ||
    //   permissions & FMS_PERMISSIONS.DELETE
    // )
  }

  // 暴露方法给父组件
  defineExpose({
    scrollToSelectedNode,
    expandToSelectedNode,
    refreshTree,
    loadTreeData,
  })
</script>

<style lang="scss" scoped>
  :deep() {
    .el-button--text {
      padding: 0 8px;
    }
  }
  .fms-directory-tree {
    height: 100%;
    display: flex;
    flex-direction: column;

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 0px;
      margin: 0 10px;
      border-bottom: 1px solid #dcdfe6;
    }

    &__title {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    &__actions {
      display: flex;
      align-items: center;
    }

    &__content {
      flex: 1;
      overflow: auto;
      padding: 8px;
    }

    &__node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 4px 4px;
      position: relative;

      // 移除节点级别的 hover 效果，因为现在由 ant-tree-treenode 处理
    }

    &__node-content {
      display: flex;
      align-items: center;
      gap: 6px;
      flex: 1;
      min-width: 0;
    }

    &__node-icon {
      font-size: 14px;
      color: var(--el-color-primary);
    }

    &__node-title {
      font-size: 13px;
      color: var(--el-text-color-primary);
      flex: 1;
      min-width: 0;

      // showTip组件的样式覆盖
      :deep(.overflow-tooltip) {
        font-size: 13px;
        color: var(--el-text-color-primary);
        line-height: 1.2;
      }
    }

    &__node-actions {
      opacity: 0;
      transition: opacity 0.2s;
    }

    &__node-more {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      padding: 2px;
      font-size: 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06);
      }
    }

    &__empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
    }
  }
  :deep() {
    .ant-tree .ant-tree-node-content-wrapper {
      // line-height: 32px;
    }
    // a-tree 节点样式定制 - 修改整个节点区域
    .ant-tree-treenode {
      // margin-bottom: 6px;
      border-radius: 6px;
      transition: background-color 0.3s;

      // 整个节点的悬停效果
      &:hover {
        background-color: #ebf1fe !important;

        .bi-fixed-right-icon {
          visibility: visible;
        }

        // 确保在节点悬停时内容区域不要有自己的背景色
        .ant-tree-node-content-wrapper {
          background-color: transparent !important;
        }
      }

      // 节点内容区域清除默认背景样式
      .ant-tree-node-content-wrapper {
        // line-height: 32px;
        background: transparent !important;
        transition: none;

        &:hover {
          background-color: transparent !important;
        }
      }
    }
  }

  // antd-vue 树组件样式覆盖 - 参考 WikiContent.vue 样式
  :deep(.ant-tree) {
    background: transparent;

    .ant-tree-treenode {
      padding: 0 !important; // 强制覆盖
      margin-bottom: 0; // itemHeight没有计算margin-bottom, 会导致树结构高度计算错误底下部分item无法滚动触及将不可视
      border-radius: 6px;
      transition: background-color 0.3s;

      // // 确保在节点悬停时内容区域不要有自己的背景色
      .ant-tree-node-content-wrapper {
        padding: 0;
        // line-height: 32px;
        background: transparent !important;
        transition: none;
      }

      // 整个节点的悬停效果
      &:hover {
        background-color: #ebf1fe !important;

        .fms-directory-tree__node-actions {
          opacity: 1;
        }
      }

      // 选中节点时整个节点的样式
      &.ant-tree-treenode-selected,
      &:has(.ant-tree-node-selected) {
        background-color: #ebf1fe !important;
        border-radius: 6px;

        .ant-tree-node-selected {
          background-color: transparent !important;
        }
      }
    }

    .ant-tree-title {
      width: 100%;
    }

    .ant-tree-switcher {
      color: #666666;
      padding-top: 6px;
    }

    .ant-tree-indent-unit {
      width: 16px;
    }

    // 滚动条样式
    .ant-tree-list-scrollbar {
      width: 6px !important;
      opacity: 0 !important;
      transition: opacity 0.3s;
    }

    .ant-tree-list-scrollbar-thumb {
      background-color: rgba(144, 147, 153, 0.3) !important;
      border-radius: 4px !important;
    }

    .ant-tree-list-holder {
      overflow-x: clip;
    }

    .ant-tree-list:hover .ant-tree-list-scrollbar,
    .ant-tree-list.scrolling .ant-tree-list-scrollbar {
      opacity: 1 !important;
    }

    // 调整树节点内部元素与整个节点的间距
    .ant-tree-list-holder-inner {
      padding: 0 6px;
    }
  }
</style>
