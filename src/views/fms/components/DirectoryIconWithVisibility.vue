<template>
  <div class="directory-icon-with-visibility">
    <!-- 主目录图标 -->
    <vab-icon
      :icon="'icon-dir'"
      is-custom-svg
      class="directory-icon-with-visibility__main-icon"
      :style="{
        width: props.size + 'px',
        height: props.size + 'px',
      }"
    />

    <!-- 可见性标识图标 -->
    <el-tooltip
      v-if="visibility && visibility !== 'public'"
      :content="getVisibilityTooltip(visibility)"
      placement="top"
      :show-after="1500"
    >
      <div class="directory-icon-with-visibility__visibility-badge">
        <!-- 部门可见 -->
        <vab-icon
          v-if="visibility === 'department'"
          icon="group-line"
          class="directory-icon-with-visibility__badge-icon"
        />

        <!-- 用户可见 -->
        <vab-icon
          v-else-if="visibility === 'user'"
          icon="admin-line"
          class="directory-icon-with-visibility__badge-icon"
        />

        <!-- 私有 -->
        <vab-icon
          v-else-if="visibility === 'private'"
          icon="lock-2-line"
          class="directory-icon-with-visibility__badge-icon"
        />
      </div>
    </el-tooltip>
  </div>
</template>

<script setup>
  // Props 定义
  const props = defineProps({
    visibility: {
      type: String,
      default: 'public',
      validator: (value) =>
        ['public', 'department', 'user', 'private'].includes(value),
    },
    size: {
      type: Number,
      default: 16,
    },
  })

  // 获取可见性提示文本
  const getVisibilityTooltip = (visibility) => {
    const tooltipMap = {
      department: '限定部门可见',
      user: '限定人员可见',
      private: '私有目录',
    }
    return tooltipMap[visibility] || ''
  }
</script>

<style lang="scss" scoped>
  .directory-icon-with-visibility {
    position: relative;
    display: inline-block;
    // margin-right: 6px;

    &__badge-icon {
      font-size: 12px;
      color: #666;
    }

    &__main-icon {
      display: block;
    }

    &__visibility-badge {
      position: absolute;
      bottom: 2px;
      color: #666;
      right: 6px;
      width: 10px;
      height: 10px;
      background-color: rgba(255, 255, 255, 0);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
      // border: 1px solid rgba(0, 0, 0, 0.1);
    }

    &__badge-icon {
      // width: 8px !important;
      // height: 8px !important;
      // font-size: 8px;

      // 部门图标颜色
      &[icon='department'] {
        color: #409eff;
      }

      // 用户图标颜色
      &[icon='admin-line'] {
        color: #67c23a;
      }

      // 私有图标颜色
      &[icon='lock-2-line'] {
        color: #f56c6c;
      }
    }

    // 悬停效果
    &:hover {
      .directory-icon-with-visibility__visibility-badge {
        background-color: rgba(255, 255, 255, 0);
        // box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      }
    }
  }
</style>
