<template>
  <div class="fms-breadcrumb-container" ref="containerRef">
    <!-- 导航按钮 -->
    <div class="breadcrumb-navigation" ref="navigationRef">
      <el-button
        text
        :disabled="!canGoBack"
        size="small"
        :icon="Back"
        @click="handleNavigation('back')"
        title="后退"
      />
      <el-button
        text
        :disabled="!canGoForward"
        size="small"
        :icon="Right"
        @click="handleNavigation('forward')"
        title="前进"
      />
      <el-button
        text
        :disabled="!canGoUp"
        size="small"
        :icon="Top"
        @click="handleNavigation('up')"
        title="返回上层目录"
      />
    </div>

    <!-- 面包屑路径 -->
    <div class="breadcrumb-path" ref="breadcrumbPathRef">
      <el-tooltip
        :content="fullPathTooltip"
        :disabled="!showTooltip"
        placement="bottom-start"
        :show-after="1500"
      >
        <el-breadcrumb separator="/">
          <el-breadcrumb-item
            v-for="(item, index) in displayItems"
            :key="item.id || 'root'"
            :class="{
              'is-link': index < displayItems.length - 1 && !item.isEllipsis,
            }"
            @click="handleClick(item, index)"
          >
            <!-- 省略号显示 -->
            <span v-if="item.isEllipsis" class="breadcrumb-ellipsis">...</span>
            <!-- 正常面包屑项 -->
            <template v-else>
              <vab-icon
                v-if="index === 0"
                icon="home-7-fill"
                :style="{
                  width: 16 + 'px',
                  height: 16 + 'px',
                  marginRight: '3px',
                  marginBottom: '15px',
                }"
              />
              <span v-if="index === 0 && item.name != '根目录'">/</span>
              <span class="breadcrumb-item-text">{{ item.name }}</span>
            </template>
          </el-breadcrumb-item>
        </el-breadcrumb>
      </el-tooltip>
    </div>

    <!-- 隐藏的测量元素 -->
    <div class="breadcrumb-measure" ref="measureRef" aria-hidden="true">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          <vab-icon
            icon="home-7-fill"
            :style="{
              width: 16 + 'px',
              height: 16 + 'px',
              marginRight: '3px',
              marginBottom: '15px',
            }"
          />
          <span class="breadcrumb-item-text">测量文本</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
  import { HomeFilled, Back, Right, Top } from '@element-plus/icons-vue'

  const props = defineProps({
    items: {
      type: Array,
      default: () => [],
    },
    canGoBack: {
      type: Boolean,
      default: false,
    },
    canGoForward: {
      type: Boolean,
      default: false,
    },
    canGoUp: {
      type: Boolean,
      default: false,
    },
    // 最大显示的面包屑项数量（不包括省略号）- 现在作为后备值
    maxVisibleItems: {
      type: Number,
      default: 4,
    },
    // 是否启用动态计算
    enableDynamicCalculation: {
      type: Boolean,
      default: true,
    },
  })

  const emit = defineEmits(['change', 'navigate'])

  // 响应式数据
  const containerRef = ref(null)
  const navigationRef = ref(null)
  const breadcrumbPathRef = ref(null)
  const measureRef = ref(null)

  const containerWidth = ref(0)
  const navigationWidth = ref(0)
  const dynamicMaxVisibleItems = ref(props.maxVisibleItems)

  // 文本宽度缓存
  const textWidthCache = ref(new Map())

  // 防抖定时器
  let resizeTimer = null
  let calculationTimer = null

  /**
   * 测量文本宽度的工具函数
   * @param {string} text - 要测量的文本
   * @param {boolean} isFirstItem - 是否为第一项（包含图标）
   * @returns {number} 文本宽度（像素）
   */
  const measureTextWidth = (text, isFirstItem = false) => {
    const cacheKey = `${text}_${isFirstItem}`

    // 检查缓存
    if (textWidthCache.value.has(cacheKey)) {
      return textWidthCache.value.get(cacheKey)
    }

    if (!measureRef.value) return 0

    const measureElement = measureRef.value.querySelector(
      '.breadcrumb-item-text'
    )
    const iconElement = measureRef.value.querySelector('.vab-icon')

    if (!measureElement) return 0

    // 设置测量文本
    measureElement.textContent = text

    // 显示/隐藏图标
    if (iconElement) {
      iconElement.style.display = isFirstItem ? 'inline-block' : 'none'
    }

    // 获取宽度
    const breadcrumbItem = measureRef.value.querySelector(
      '.el-breadcrumb__item'
    )
    const width = breadcrumbItem ? breadcrumbItem.offsetWidth : 0

    // 缓存结果
    textWidthCache.value.set(cacheKey, width)

    return width
  }

  /**
   * 计算所有面包屑项的总宽度
   * @param {Array} items - 面包屑项数组
   * @returns {number} 总宽度
   */
  const calculateTotalItemsWidth = (items) => {
    if (!items || items.length === 0) return 0

    let totalWidth = 0

    items.forEach((item, index) => {
      const isFirstItem = index === 0
      const itemWidth = measureTextWidth(item.name, isFirstItem)
      totalWidth += itemWidth

      // 添加分隔符宽度（除了最后一项）
      if (index < items.length - 1) {
        totalWidth += 20 // 分隔符大约占用20px
      }
    })

    return totalWidth
  }

  /**
   * 动态计算最大可见项数量
   */
  const calculateDynamicMaxItems = () => {
    if (
      !props.enableDynamicCalculation ||
      !props.items ||
      props.items.length === 0
    ) {
      dynamicMaxVisibleItems.value = props.maxVisibleItems
      return
    }

    // 清除之前的计算定时器
    if (calculationTimer) {
      clearTimeout(calculationTimer)
    }

    calculationTimer = setTimeout(() => {
      const availableWidth = containerWidth.value - navigationWidth.value - 40 // 40px 为间距和边距

      if (availableWidth <= 0) {
        dynamicMaxVisibleItems.value = 1
        return
      }

      const items = props.items
      const totalItemsWidth = calculateTotalItemsWidth(items)

      // 如果所有项都能显示，直接返回所有项数量
      if (totalItemsWidth <= availableWidth) {
        dynamicMaxVisibleItems.value = items.length
        return
      }

      // 计算省略号宽度
      const ellipsisWidth = measureTextWidth('...', false)

      // 优先保证最后一项和第一项可见
      const firstItemWidth = measureTextWidth(items[0].name, true)
      const lastItemWidth = measureTextWidth(
        items[items.length - 1].name,
        false
      )

      // 基础宽度：第一项 + 省略号 + 最后一项 + 分隔符
      let baseWidth = firstItemWidth + ellipsisWidth + lastItemWidth + 40 // 40px 分隔符

      if (baseWidth > availableWidth) {
        // 空间太小，只显示最后一项
        dynamicMaxVisibleItems.value = 1
        return
      }

      // 计算还能显示多少项
      let remainingWidth = availableWidth - baseWidth
      let maxItems = 2 // 已经包含第一项和最后一项

      // 从倒数第二项开始，向前尝试添加项目
      for (let i = items.length - 2; i > 0 && remainingWidth > 0; i--) {
        const itemWidth = measureTextWidth(items[i].name, false) + 20 // 包含分隔符

        if (itemWidth <= remainingWidth) {
          remainingWidth -= itemWidth
          maxItems++
        } else {
          break
        }
      }

      // 确保至少显示2项（如果有的话），最多不超过总项数
      dynamicMaxVisibleItems.value = Math.max(
        2,
        Math.min(maxItems, items.length)
      )
    }, 50) // 50ms 防抖
  }

  /**
   * 更新容器尺寸信息
   */
  const updateDimensions = () => {
    if (containerRef.value) {
      containerWidth.value = containerRef.value.offsetWidth
    }

    if (navigationRef.value) {
      navigationWidth.value = navigationRef.value.offsetWidth
    }
  }

  /**
   * 防抖的尺寸更新函数
   */
  const debouncedUpdateDimensions = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }

    resizeTimer = setTimeout(() => {
      updateDimensions()
      calculateDynamicMaxItems()
    }, 150) // 150ms 防抖
  }

  // 计算显示的面包屑项
  const displayItems = computed(() => {
    const items = props.items
    if (!items || items.length === 0) return []

    const maxVisible = props.enableDynamicCalculation
      ? dynamicMaxVisibleItems.value
      : props.maxVisibleItems

    // 如果项目数量小于等于最大显示数量，直接返回所有项目
    if (items.length <= maxVisible) {
      return items
    }

    // 需要省略中间部分
    const result = []

    if (maxVisible >= 3) {
      // 显示首项
      result.push(items[0])

      // 添加省略号
      result.push({
        id: 'ellipsis',
        name: '...',
        isEllipsis: true,
      })

      // 显示最后几项（根据maxVisible决定）
      const lastItemsCount = maxVisible - 2 // 减去首项和省略号
      const startIndex = items.length - lastItemsCount
      for (let i = startIndex; i < items.length; i++) {
        result.push(items[i])
      }
    } else if (maxVisible === 2) {
      // 只显示首项和末项
      result.push(items[0])
      if (items.length > 2) {
        result.push({
          id: 'ellipsis',
          name: '...',
          isEllipsis: true,
        })
      }
      result.push(items[items.length - 1])
    } else {
      // maxVisible为1或更少，只显示末项
      result.push(items[items.length - 1])
    }

    return result
  })

  // 计算完整路径的tooltip内容
  const fullPathTooltip = computed(() => {
    if (!props.items || props.items.length === 0) return ''
    return props.items.map((item) => item.name).join(' / ')
  })

  // 是否显示tooltip
  const showTooltip = computed(() => {
    const maxVisible = props.enableDynamicCalculation
      ? dynamicMaxVisibleItems.value
      : props.maxVisibleItems
    return props.items && props.items.length > maxVisible
  })

  const handleClick = (item, index) => {
    // 如果是省略号，不处理点击
    if (item.isEllipsis) return

    // 找到原始项目的索引
    const originalIndex = props.items.findIndex(
      (originalItem) =>
        originalItem.id === item.id && originalItem.name === item.name
    )

    if (originalIndex !== -1 && originalIndex < props.items.length - 1) {
      emit('change', item)
    }
  }

  // 处理导航操作
  const handleNavigation = (action) => {
    emit('navigate', action)
  }

  // 监听窗口大小变化
  const handleResize = () => {
    debouncedUpdateDimensions()
  }

  // 监听items变化，重新计算
  watch(
    () => props.items,
    () => {
      // 清空缓存
      textWidthCache.value.clear()

      nextTick(() => {
        updateDimensions()
        calculateDynamicMaxItems()
      })
    },
    { deep: true }
  )

  // 监听动态计算开关
  watch(
    () => props.enableDynamicCalculation,
    () => {
      if (props.enableDynamicCalculation) {
        calculateDynamicMaxItems()
      } else {
        dynamicMaxVisibleItems.value = props.maxVisibleItems
      }
    }
  )

  onMounted(() => {
    nextTick(() => {
      updateDimensions()
      calculateDynamicMaxItems()
    })
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)

    // 清理定时器
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
    if (calculationTimer) {
      clearTimeout(calculationTimer)
    }
  })
</script>

<style scoped lang="scss">
  :deep() {
    .el-breadcrumb__inner {
      line-height: 2rem;
    }
  }

  .fms-breadcrumb-container {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    position: relative;

    .breadcrumb-navigation {
      display: flex;
      flex-shrink: 0;

      .el-button {
        width: 28px;
        height: 28px;
        padding: 0;

        &:disabled {
          opacity: 0.5;
        }
      }
    }

    .breadcrumb-path {
      flex: 1;
      min-width: 0;
      overflow: hidden;

      :deep(.el-breadcrumb) {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        overflow: hidden;
      }

      :deep(.el-breadcrumb__item) {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        &:last-child {
          flex-shrink: 1;
          min-width: 0;
        }
      }

      :deep(.el-breadcrumb__inner) {
        display: flex;
        align-items: center;
        max-width: 100%;

        &.is-link {
          color: var(--el-text-color-regular);

          &:hover {
            color: var(--el-color-primary);
          }
        }
      }

      .breadcrumb-item-text {
        display: inline-block;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
      }

      .breadcrumb-ellipsis {
        color: var(--el-text-color-placeholder);
        font-weight: normal;
        padding: 0 4px;
        user-select: none;
        cursor: default;
      }
    }

    // 隐藏的测量元素
    .breadcrumb-measure {
      position: absolute;
      top: -9999px;
      left: -9999px;
      visibility: hidden;
      pointer-events: none;
      white-space: nowrap;

      // 确保样式与实际显示元素一致
      font-family: inherit;
      font-size: inherit;
      font-weight: inherit;
      line-height: inherit;
    }

    .is-link {
      cursor: pointer;
    }

    .is-link:hover {
      color: var(--el-color-primary);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .fms-breadcrumb-container {
      gap: 8px;

      .breadcrumb-navigation {
        .el-button {
          width: 24px;
          height: 24px;
        }
      }

      .breadcrumb-path {
        .breadcrumb-item-text {
          max-width: 120px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .fms-breadcrumb-container {
      gap: 6px;

      .breadcrumb-path {
        .breadcrumb-item-text {
          max-width: 80px;
        }
      }
    }
  }

  // 大屏幕优化
  @media (min-width: 1200px) {
    .fms-breadcrumb-container {
      .breadcrumb-path {
        .breadcrumb-item-text {
          max-width: 300px;
        }
      }
    }
  }
</style>
