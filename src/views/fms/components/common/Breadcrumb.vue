<template>
  <div class="fms-breadcrumb">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item
        v-for="(item, index) in items"
        :key="index"
        :to="item.to"
        @click="handleItemClick(item, index)"
      >
        <span class="fms-breadcrumb__item">
          <el-icon v-if="item.icon" class="fms-breadcrumb__icon">
            <component :is="item.icon" />
          </el-icon>
          {{ item.label }}
        </span>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
  import { defineProps, defineEmits } from 'vue'

  // Props 定义
  const props = defineProps({
    items: {
      type: Array,
      default: () => [],
      validator: (items) => {
        return items.every(
          (item) => typeof item === 'object' && typeof item.label === 'string'
        )
      },
    },
  })

  // Events 定义
  const emit = defineEmits(['change'])

  // 方法
  const handleItemClick = (item, index) => {
    if (item.to) {
      emit('change', item, index)
    }
  }
</script>

<style lang="scss" scoped>
  .fms-breadcrumb {
    &__item {
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }

    &__icon {
      font-size: 14px;
    }
  }

  :deep(.el-breadcrumb__item) {
    .el-breadcrumb__inner {
      color: var(--el-text-color-regular);
      font-weight: normal;

      &:hover {
        color: var(--el-color-primary);
      }
    }

    &:last-child .el-breadcrumb__inner {
      color: var(--el-text-color-primary);
      font-weight: 500;
    }
  }
</style>
