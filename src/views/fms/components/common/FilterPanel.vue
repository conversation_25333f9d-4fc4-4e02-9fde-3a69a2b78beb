<template>
  <div class="fms-filter-panel">
    <VabQueryForm>
      <template #left>
        <div class="fms-filter-panel__fields">
          <el-form
            ref="formRef"
            :model="formData"
            :inline="true"
            class="fms-filter-panel__form"
          >
            <el-form-item
              v-for="field in fields"
              :key="field.prop"
              :label="field.label"
              :prop="field.prop"
              class="fms-filter-panel__form-item"
            >
              <!-- 输入框 -->
              <el-input
                v-if="field.type === 'input'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                :clearable="true"
                class="fms-filter-panel__input"
              />

              <!-- 选择器 -->
              <el-select
                v-else-if="field.type === 'select'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                :multiple="field.multiple"
                :clearable="true"
                class="fms-filter-panel__select"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 日期范围选择器 -->
              <el-date-picker
                v-else-if="field.type === 'daterange'"
                v-model="formData[field.prop]"
                type="daterange"
                :placeholder="field.placeholder"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="fms-filter-panel__date-picker"
              />

              <!-- 日期时间选择器 -->
              <el-date-picker
                v-else-if="field.type === 'datetime'"
                v-model="formData[field.prop]"
                type="datetime"
                :placeholder="field.placeholder"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="fms-filter-panel__date-picker"
              />

              <!-- 数字输入框 -->
              <el-input-number
                v-else-if="field.type === 'number'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                :min="field.min"
                :max="field.max"
                :step="field.step"
                class="fms-filter-panel__number"
              />

              <!-- 文本域 -->
              <el-input
                v-else-if="field.type === 'textarea'"
                v-model="formData[field.prop]"
                type="textarea"
                :placeholder="field.placeholder"
                :rows="field.rows || 2"
                class="fms-filter-panel__textarea"
              />
            </el-form-item>
          </el-form>
        </div>
      </template>

      <template #right>
        <div class="fms-filter-panel__actions">
          <el-button
            type="primary"
            :icon="Search"
            :loading="loading"
            @click="handleSubmit"
          >
            查询
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          <el-button
            v-if="showAdvanced"
            :icon="expanded ? ArrowUp : ArrowDown"
            @click="toggleExpanded"
          >
            {{ expanded ? '收起' : '展开' }}
          </el-button>
        </div>
      </template>
    </VabQueryForm>
  </div>
</template>

<script setup>
  import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
  import { Search, Refresh, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

  // Props 定义
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    fields: {
      type: Array,
      default: () => [],
      validator: (fields) => {
        return fields.every(
          (field) =>
            typeof field === 'object' &&
            typeof field.prop === 'string' &&
            typeof field.label === 'string' &&
            typeof field.type === 'string'
        )
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
    showAdvanced: {
      type: Boolean,
      default: false,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:modelValue', 'submit', 'reset'])

  // 响应式数据
  const formRef = ref()
  const formData = reactive({ ...props.modelValue })
  const expanded = ref(false)

  // 监听 modelValue 变化
  watch(
    () => props.modelValue,
    (newValue) => {
      Object.assign(formData, newValue)
    },
    { deep: true }
  )

  // 监听表单数据变化
  watch(
    formData,
    (newValue) => {
      emit('update:modelValue', { ...newValue })
    },
    { deep: true }
  )

  // 方法
  const handleSubmit = () => {
    // 过滤空值
    const filteredData = {}
    Object.keys(formData).forEach((key) => {
      const value = formData[key]
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          filteredData[key] = value
        } else if (!Array.isArray(value)) {
          filteredData[key] = value
        }
      }
    })

    emit('submit', filteredData)
  }

  const handleReset = () => {
    // 重置表单数据
    Object.keys(formData).forEach((key) => {
      const field = props.fields.find((f) => f.prop === key)
      if (field) {
        if (field.multiple || field.type === 'daterange') {
          formData[key] = []
        } else {
          formData[key] = ''
        }
      }
    })

    // 重置表单验证
    if (formRef.value) {
      formRef.value.resetFields()
    }

    emit('reset')
  }

  const toggleExpanded = () => {
    expanded.value = !expanded.value
  }

  // 初始化表单数据
  const initFormData = () => {
    props.fields.forEach((field) => {
      if (!(field.prop in formData)) {
        if (field.multiple || field.type === 'daterange') {
          formData[field.prop] = []
        } else {
          formData[field.prop] = ''
        }
      }
    })
  }

  // 组件挂载时初始化
  initFormData()
</script>

<style lang="scss" scoped>
  .fms-filter-panel {
    margin-bottom: 16px;

    &__form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 16px;
      }
    }

    &__form-item {
      .el-form-item__label {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }

    &__input,
    &__select,
    &__number {
      width: 200px;
    }

    &__date-picker {
      width: 240px;
    }

    &__textarea {
      width: 300px;
    }

    &__actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-button {
        margin-left: 0;
      }
    }
  }

  :deep(.vab-query-form) {
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    padding: 16px;
  }
</style>
