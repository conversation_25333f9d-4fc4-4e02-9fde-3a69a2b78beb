<template>
  <firefly-dialog
    v-model="visible"
    title="上传文件"
    width="600px"
    max-height="70vh"
    @close="handleClose"
  >
    <el-upload
      ref="uploadRef"
      :action="uploadAction"
      :headers="uploadHeaders"
      :data="uploadData"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :before-upload="beforeUpload"
      :auto-upload="false"
      :show-file-list="false"
      multiple
      drag
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        将文件拖到此处，或
        <em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">
          支持多文件上传，单个文件大小不超过 100MB
        </div>
      </template>
    </el-upload>

    <!-- 上传配置表单 -->
    <div v-if="uploadList.length > 0" class="file-upload-config">
      <el-form :model="formData" label-width="72px" label-position="left">
        <el-form-item label="可见性" prop="visibility">
          <el-radio-group v-model="formData.visibility">
            <el-radio label="public">
              <el-icon><View /></el-icon>
              公开
            </el-radio>
            <el-radio label="department">
              <vab-icon
                icon="department"
                is-custom-svg
                style="width: 22px; height: 22px; margin-right: 0px"
              />
              部门
            </el-radio>
            <el-radio label="user">
              <vab-icon icon="admin-line" />
              用户
            </el-radio>
            <el-radio label="private">
              <vab-icon icon="lock-2-line" />
              私有
            </el-radio>
          </el-radio-group>
          <div class="file-upload-config__visibility-help">
            <el-text type="info">
              公开：所有人可见；部门：仅指定部门内可见；用户：仅指定用户可见；私有：仅创建者可见
            </el-text>
          </div>
        </el-form-item>

        <!-- 用户选择 -->
        <el-form-item
          v-if="formData.visibility === 'user'"
          label="选择用户"
          prop="selectedUsers"
        >
          <template
            v-if="formData.selectedUsers && formData.selectedUsers.length > 0"
          >
            <div class="file-upload-config__user-info">
              <span
                v-for="(user, index) in formData.selectedUsers"
                :key="index"
                class="file-upload-config__user-item member-item"
              >
                {{ user.name }}
                <vab-icon icon="close-fill" @click="removeUser(user.id)" />
              </span>
            </div>
          </template>
          <vab-icon
            icon="add-circle-fill"
            style="font-size: 20px; color: #1890ff; cursor: pointer"
            @click="handleUserSelect"
          />
        </el-form-item>

        <!-- 部门选择 -->
        <el-form-item
          v-if="formData.visibility === 'department'"
          label="选择部门"
          prop="selectedDepartments"
        >
          <el-cascader
            v-model="formData.selectedDepartments"
            :options="departmentOptions"
            clearable
            filterable
            collapse-tags
            :show-all-levels="false"
            :props="{
              expandTrigger: 'hover',
              multiple: true,
              value: 'id',
              label: 'name',
              emitPath: false,
            }"
            placeholder="请选择部门"
            style="width: 220px"
          />
        </el-form-item>

        <!-- 私有用户显示 -->
        <el-form-item
          v-if="formData.visibility === 'private'"
          label="选择用户"
          prop="privateUser"
        >
          <el-input
            v-model="currentUserName"
            readonly
            disabled
            placeholder="当前用户"
          >
            <template #prefix>
              <vab-icon icon="admin-line" />
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="uploadList.length > 0" class="file-upload-list">
      <h4>上传列表</h4>
      <div v-for="file in uploadList" :key="file.uid" class="file-upload-item">
        <div class="file-upload-item__info">
          <span class="file-upload-item__name">{{ file.name }}</span>
          <span class="file-upload-item__size">
            {{ formatFileSize(file.size) }}
          </span>
        </div>
        <div class="file-upload-item__progress">
          <el-progress
            :percentage="file.percentage || 0"
            :status="
              file.status === 'success'
                ? 'success'
                : file.status === 'error'
                ? 'exception'
                : ''
            "
          />
        </div>
        <div class="file-upload-item__actions">
          <el-button
            v-if="file.status !== 'success'"
            type="text"
            @click="removeFile(file)"
          >
            移除
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="clearFiles">清空</el-button>
        <el-button
          type="primary"
          @click="startUpload"
          :loading="uploading"
          :disabled="uploadList.length === 0"
        >
          开始上传
        </el-button>
      </span>
    </template>
    <!-- 用户选择组件 -->
    <MemberSelect
      v-model:visible="showMemberVisible"
      :user-id-arr="state.userRedmineIdArr"
      @confirm="addMember"
    />
  </firefly-dialog>
</template>

<script setup>
  import { ref, reactive, watch, computed, inject, onMounted } from 'vue'
  import {
    UploadFilled,
    View,
    OfficeBuilding,
    User,
    Hide,
  } from '@element-plus/icons-vue'
  import { getToken } from '@/utils/token'
  import { FmsUploadServer } from '@/api/setting'
  import { storeToRefs } from 'pinia'
  import { useUserStore } from '@/store/modules/user'
  import MemberSelect from '@/components/MemberSelect'
  import { getDepartment } from '@/api/oaReport'
  import { queryPersonnel } from '@/api/user'
  import { usePermissionStore } from '@/views/fms/stores/permissionStore'

  const $baseMessage = inject('$baseMessage')
  const permissionStore = usePermissionStore()

  // Store
  const userStore = useUserStore()
  const { user_id, username } = storeToRefs(userStore)

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    directoryId: {
      type: [Number, String],
      default: null,
    },
    visibility: {
      type: String,
      default: 'public',
      validator: (value) =>
        ['public', 'department', 'user', 'private'].includes(value),
    },
    checkDuplicate: {
      type: Boolean,
      default: true,
    },
  })

  const emit = defineEmits(['update:modelValue', 'success'])

  const visible = ref(false)
  const uploading = ref(false)
  const uploadRef = ref()
  const uploadList = ref([])
  const departmentOptions = ref([])
  const showMemberVisible = ref(false)
  const state = reactive({
    userIdArr: [],
    userRedmineIdArr: [],
  })

  // 表单数据
  const formData = reactive({
    visibility: props.visibility,
    selectedUsers: [],
    selectedDepartments: [],
  })

  // 当前用户名称
  const currentUserName = computed(() => {
    return username.value || '当前用户'
  })

  // 上传配置
  const uploadAction = FmsUploadServer
  const uploadHeaders = computed(() => ({
    Authorization: `Bearer ${getToken()}`,
  }))
  const uploadData = computed(() => {
    // 构造权限数组
    let permissions = []
    if (formData.visibility === 'user') {
      permissions = formData.selectedUsers.map((user) => ({
        subject_type: 'user',
        subject_id: user.id,
        subject_name: user.name,
        permission_set: permissionStore.PERMISSION_BITS.VIEW,
      }))
    } else if (formData.visibility === 'department') {
      // 需要获取部门名称，从departmentOptions中查找
      permissions = formData.selectedDepartments.map((deptId) => {
        const findDeptName = (options, id) => {
          for (const option of options) {
            if (option.id === id) {
              return option.name
            }
            if (option.children) {
              const found = findDeptName(option.children, id)
              if (found) return found
            }
          }
          return null
        }
        const deptName = findDeptName(departmentOptions.value, deptId)
        return {
          subject_type: 'dept',
          subject_id: deptId,
          subject_name: deptName || '未知部门',
          permission_set: permissionStore.PERMISSION_BITS.VIEW,
        }
      })
    } else if (formData.visibility === 'private') {
      permissions = [
        {
          subject_type: 'user',
          subject_id: user_id.value,
          subject_name: currentUserName.value,
          permission_set: permissionStore.PERMISSION_BITS.VIEW,
        },
      ]
    }

    return {
      directory_id: props.directoryId,
      visibility: formData.visibility,
      permissions: JSON.stringify(permissions), // 序列化为JSON字符串
    }
  })

  // 监听显示状态
  watch(
    () => props.modelValue,
    (val) => {
      visible.value = val
      if (!val) {
        clearFiles()
        // 重置表单数据
        formData.visibility = props.visibility
        formData.selectedUsers = []
        formData.selectedDepartments = []
      }
    }
  )

  watch(visible, (val) => {
    emit('update:modelValue', val)
  })

  // 监听props变化，同步到表单数据
  watch(
    () => [props.visibility],
    ([newVisibility]) => {
      formData.visibility = newVisibility
    }
  )

  // 监听可见性变化，清空相关选择
  watch(
    () => formData.visibility,
    (newVisibility) => {
      if (newVisibility === 'user') {
        formData.selectedDepartments = []
      } else if (newVisibility === 'department') {
        formData.selectedUsers = []
      } else if (newVisibility === 'private') {
        formData.selectedUsers = []
        formData.selectedDepartments = []
      } else {
        formData.selectedUsers = []
        formData.selectedDepartments = []
      }
    }
  )

  const handleClose = () => {
    visible.value = false
  }

  const handleFileChange = (file, fileList) => {
    // 同步更新uploadList
    uploadList.value = fileList.map((f) => ({
      uid: f.uid,
      name: f.name,
      size: f.size,
      status: f.status || 'ready',
      percentage: f.percentage || 0,
      raw: f.raw || f,
    }))
  }

  const handleFileRemove = (file, fileList) => {
    // 同步更新uploadList
    uploadList.value = fileList.map((f) => ({
      uid: f.uid,
      name: f.name,
      size: f.size,
      status: f.status || 'ready',
      percentage: f.percentage || 0,
      raw: f.raw || f,
    }))
  }

  const beforeUpload = (file) => {
    // 检查文件大小
    const isLt100M = file.size / 1024 / 1024 < 100
    if (!isLt100M) {
      $baseMessage('文件大小不能超过 100MB!', 'error', 'vab-hey-message-error')
      return false
    }
    return true // 允许上传
  }

  const handleProgress = (event, file) => {
    const item = uploadList.value.find((item) => item.uid === file.uid)
    if (item) {
      item.percentage = Math.round(event.percent)
      item.status = 'uploading'
    }
  }

  const handleSuccess = async (response, file) => {
    const item = uploadList.value.find((item) => item.uid === file.uid)
    if (item) {
      item.status = 'success'
      item.percentage = 100
    }

    // 检查是否所有文件都上传完成
    const allSuccess = uploadList.value.every(
      (item) => item.status === 'success'
    )
    if (allSuccess) {
      uploading.value = false
      $baseMessage('所有文件上传成功', 'success', 'vab-hey-message-success')
      emit('success')
      await new Promise((resolve) => setTimeout(resolve, 1000))
      handleClose()
    }
  }

  const handleError = (error, file) => {
    const item = uploadList.value.find((item) => item.uid === file.uid)
    if (item) {
      item.status = 'error'
    }
    uploading.value = false
    $baseMessage(
      '文件上传失败：' + (error.message || '未知错误'),
      'error',
      'vab-hey-message-error'
    )
  }

  const startUpload = () => {
    if (uploadList.value.length === 0) {
      $baseMessage('请先选择文件', 'warning', 'vab-hey-message-warning')
      return
    }

    uploading.value = true
    uploadRef.value?.submit()
  }

  const removeFile = (file) => {
    const index = uploadList.value.findIndex((item) => item.uid === file.uid)
    if (index > -1) {
      uploadList.value.splice(index, 1)
    }
    uploadRef.value?.handleRemove(file.raw)
  }

  const clearFiles = () => {
    uploadList.value = []
    uploadRef.value?.clearFiles()
  }

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 用户选择相关方法
  const handleUserSelect = () => {
    // 设置已选用户ID数组
    state.userRedmineIdArr = formData.selectedUsers.map(
      (user) => user.third.third_user_id
    )
    showMemberVisible.value = true
  }

  const removeUser = (userId) => {
    formData.selectedUsers = formData.selectedUsers.filter(
      (user) => user.id !== userId
    )
  }

  const addMember = (options) => {
    let users = options.userList ?? []
    // 添加选中的用户到formData.selectedUsers
    formData.selectedUsers = users
      .filter((user) => user.biz_mail)
      ?.map((user) => ({ ...user }))

    showMemberVisible.value = false
  }

  // 获取部门数据
  const fetchDataDepart = async () => {
    try {
      let query = {
        filter: {
          parentid: 0,
        },
        op: {
          parentid: '>',
        },
      }
      const { data } = await getDepartment(query)
      departmentOptions.value = data
    } catch (error) {
      console.error('获取部门数据失败:', error)
      $baseMessage('获取部门数据失败', 'error', 'vab-hey-message-error')
    }
  }

  // 组件挂载时获取部门数据
  onMounted(() => {
    fetchDataDepart()
  })
</script>

<style scoped>
  .file-upload-config {
    margin-top: 20px;
    padding: 16px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 6px;
    border: 1px solid var(--el-border-color-light);
  }

  .file-upload-config__visibility-help {
    margin-top: 8px;
  }

  .file-upload-config :deep(.el-radio) {
    margin-right: 16px;
    margin-bottom: 8px;
  }

  .file-upload-config :deep(.el-radio__label) {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .file-upload-list {
    margin-top: 20px;
  }

  .file-upload-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .file-upload-item__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .file-upload-item__name {
    font-weight: 500;
  }

  .file-upload-item__size {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .file-upload-item__progress {
    flex: 1;
    margin: 0 16px;
  }

  .file-upload-item__actions {
    width: 60px;
    text-align: right;
  }

  .file-upload-config__user-info {
    display: flex;
    flex-flow: wrap;
    align-items: center;
  }

  .file-upload-config__user-item {
    padding: 7px 10px 5px 10px;
    margin-right: 10px;
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 14px;
    color: #333333;
    background: #f2f4f6;
    border-radius: 4px;

    .vab-icon {
      cursor: pointer;
      font-size: 14px;
      color: var(--el-color-primary-light-3);
      transition: color 0.2s;

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  :deep(.el-radio) {
    display: flex;
    align-items: center;
    margin-right: 24px;
    margin-bottom: 8px;

    .el-radio__label {
      display: flex;
      align-items: center;
      gap: 4px;
      padding-left: 8px;
    }
  }
</style>
