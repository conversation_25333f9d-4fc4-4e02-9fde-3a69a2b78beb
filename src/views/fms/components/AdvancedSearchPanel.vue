<template>
  <div class="advanced-search-panel">
    <!-- 搜索面板切换按钮 -->
    <div class="advanced-search-panel__toggle">
      <el-button
        type="text"
        :icon="expanded ? ArrowUp : ArrowDown"
        @click="togglePanel"
        class="advanced-search-panel__toggle-btn"
      >
        {{ expanded ? '收起筛选' : '高级筛选' }}
      </el-button>
    </div>

    <!-- 筛选面板内容 -->
    <el-collapse-transition>
      <div v-show="expanded" class="advanced-search-panel__content">
        <el-form
          :model="searchForm"
          label-width="80px"
          label-position="left"
          class="advanced-search-panel__form"
        >
          <!-- 文件格式筛选 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="文件格式">
                <el-select
                  v-model="searchForm.fileTypes"
                  multiple
                  placeholder="选择文件格式"
                  collapse-tags
                  collapse-tags-tooltip
                  style="width: 100%"
                  :teleported="false"
                >
                  <el-option-group
                    v-for="group in fileTypeGroups"
                    :key="group.label"
                    :label="group.label"
                  >
                    <el-option
                      v-for="item in group.options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-option-group>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 文件大小范围 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="文件大小">
                <div class="advanced-search-panel__size-range">
                  <el-input
                    v-model="searchForm.sizeMin"
                    placeholder="最小"
                    type="number"
                    min="0"
                    style="width: 36%"
                  />
                  <span class="advanced-search-panel__size-separator">-</span>
                  <el-input
                    v-model="searchForm.sizeMax"
                    placeholder="最大"
                    type="number"
                    min="0"
                    style="width: 36%"
                  />
                  <el-select
                    v-model="searchForm.sizeUnit"
                    style="width: 28%"
                    :teleported="false"
                  >
                    <el-option label="B" value="B" />
                    <el-option label="KB" value="KB" />
                    <el-option label="MB" value="MB" />
                    <el-option label="GB" value="GB" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 创建时间筛选 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="创建时间">
                <el-date-picker
                  :teleported="false"
                  v-model="searchForm.createdTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 修改时间筛选 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="修改时间">
                <el-date-picker
                  :teleported="false"
                  v-model="searchForm.updatedTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 可见性筛选 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="可见性">
                <el-select
                  v-model="searchForm.visibility"
                  multiple
                  placeholder="选择可见性"
                  style="width: 100%"
                  :teleported="false"
                >
                  <el-option label="公开" value="public" />
                  <el-option label="部门" value="department" />
                  <el-option label="用户" value="user" />
                  <el-option label="私有" value="private" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 操作按钮 -->
          <el-row>
            <el-col :span="24">
              <div class="advanced-search-panel__actions">
                <el-button
                  type="primary"
                  @click="handleSearch"
                  :loading="searching"
                >
                  应用筛选
                </el-button>
                <!-- <el-button @click="handleReset">重置</el-button> -->
                <!-- <el-button type="text" @click="togglePanel">收起</el-button> -->
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

  // Props 定义
  const props = defineProps({
    // 是否默认展开
    defaultExpanded: {
      type: Boolean,
      default: false,
    },
    // 搜索状态
    searching: {
      type: Boolean,
      default: false,
    },
  })

  // Events 定义
  const emit = defineEmits(['search', 'reset'])

  // 响应式数据
  const expanded = ref(props.defaultExpanded)

  // 搜索表单数据
  const searchForm = reactive({
    fileTypes: [], // 文件格式
    sizeMin: '', // 最小文件大小
    sizeMax: '', // 最大文件大小
    sizeUnit: 'MB', // 文件大小单位
    createdTimeRange: [], // 创建时间范围
    updatedTimeRange: [], // 修改时间范围
    visibility: [], // 可见性
  })

  // 文件格式分组选项
  const fileTypeGroups = [
    {
      label: '媒体文件',
      options: [
        {
          label: '图片',
          value: 'image/jpeg,image/png,image/gif,image/webp,image/svg+xml',
        },
        {
          label: '视频',
          value: 'video/mp4,video/webm,video/avi,video/quicktime',
        },
        {
          label: '音频',
          value: 'audio/mpeg,audio/wav,audio/ogg,audio/aac',
        },
        {
          label: '压缩包',
          value:
            'application/zip,application/x-rar-compressed,application/x-7z-compressed,application/x-tar',
        },
      ],
    },
    {
      label: '文档',
      options: [
        { label: 'PDF', value: 'application/pdf' },
        {
          label: 'Word',
          value:
            'application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        },
        {
          label: 'Excel',
          value:
            'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
        {
          label: 'PowerPoint',
          value:
            'application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation',
        },
        {
          label: '文本',
          value: 'text/plain,text/html,text/css,text/javascript',
        },
      ],
    },
  ]

  // 方法
  const togglePanel = () => {
    expanded.value = !expanded.value
  }

  const handleSearch = () => {
    // 构建搜索参数
    const searchParams = buildSearchParams()
    emit('search', searchParams)
  }

  const handleReset = () => {
    // 重置表单
    Object.assign(searchForm, {
      fileTypes: [],
      sizeMin: '',
      sizeMax: '',
      sizeUnit: 'MB',
      createdTimeRange: [],
      updatedTimeRange: [],
      visibility: [],
    })

    emit('reset')
  }

  // 构建搜索参数
  const buildSearchParams = () => {
    const params = {}

    // 文件格式筛选
    if (searchForm.fileTypes.length > 0) {
      // 将多个MIME类型合并
      const mimeTypes = searchForm.fileTypes.flatMap((type) => type.split(','))
      params.mime_types = mimeTypes
    }

    // 文件大小筛选
    if (searchForm.sizeMin || searchForm.sizeMax) {
      const unit = searchForm.sizeUnit
      const multiplier =
        {
          B: 1,
          KB: 1024,
          MB: 1024 * 1024,
          GB: 1024 * 1024 * 1024,
        }[unit] || 1

      if (searchForm.sizeMin) {
        params.size_min = parseInt(searchForm.sizeMin) * multiplier
      }
      if (searchForm.sizeMax) {
        params.size_max = parseInt(searchForm.sizeMax) * multiplier
      }
    }

    // 时间范围筛选
    // 创建时间筛选
    if (
      searchForm.createdTimeRange &&
      searchForm.createdTimeRange.length === 2
    ) {
      params.created_time_start = searchForm.createdTimeRange[0] + ' 00:00:00'
      params.created_time_end = searchForm.createdTimeRange[1] + ' 23:59:59'
    }

    // 修改时间筛选
    if (
      searchForm.updatedTimeRange &&
      searchForm.updatedTimeRange.length === 2
    ) {
      params.updated_time_start = searchForm.updatedTimeRange[0] + ' 00:00:00'
      params.updated_time_end = searchForm.updatedTimeRange[1] + ' 23:59:59'
    }

    // 可见性筛选
    if (searchForm.visibility.length > 0) {
      params.visibility = searchForm.visibility
    }

    return params
  }

  // 暴露方法
  defineExpose({
    togglePanel,
    handleReset,
    searchForm,
  })
</script>

<style scoped>
  .advanced-search-panel {
    margin-bottom: 16px;
  }

  .advanced-search-panel__toggle {
    text-align: right;
    margin-bottom: 8px;
  }

  .advanced-search-panel__toggle-btn {
    font-size: 14px;
    padding: 4px 8px;
  }

  .advanced-search-panel__content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
  }

  .advanced-search-panel__form {
    margin: 0;
  }

  .advanced-search-panel__size-range {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .advanced-search-panel__size-separator {
    color: #909399;
    font-size: 14px;
  }

  .advanced-search-panel__actions {
    text-align: end;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
  }

  .advanced-search-panel__actions .el-button {
    margin: 0 8px;
  }

  /* 深度选择器样式 */
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #333;
  }

  /* 每行筛选项之间的间距 */
  :deep(.el-row) {
    margin-bottom: 8px;
  }

  :deep(.el-row:last-child) {
    margin-bottom: 0;
  }
</style>
