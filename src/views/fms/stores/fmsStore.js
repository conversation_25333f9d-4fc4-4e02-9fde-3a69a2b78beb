/**
 * FMS 主状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useFmsStore = defineStore('fms', () => {
  // 状态
  const currentDirectoryId = ref(null)
  const currentDirectory = ref(null)
  const breadcrumbPath = ref([])
  const searchKeyword = ref('')
  const viewMode = ref('mixed') // 'directory' | 'file' | 'mixed'
  const selectedItems = ref([])
  const isLoading = ref(false)
  const error = ref(null)

  // 用户信息
  const currentUser = ref(null)

  // 系统配置
  const config = ref({
    maxFileSize: 100 * 1024 * 1024, // 100MB
    allowedFileTypes: [],
    uploadChunkSize: 2 * 1024 * 1024, // 2MB
    previewEnabled: true,
    thumbnailEnabled: true,
  })

  // 计算属性
  const hasSelection = computed(() => selectedItems.value.length > 0)

  const selectedDirectories = computed(() =>
    selectedItems.value.filter((item) => item.type === 'directory')
  )

  const selectedFiles = computed(() =>
    selectedItems.value.filter((item) => item.type === 'file')
  )

  const canBatchDelete = computed(() =>
    selectedItems.value.every((item) => item.canDelete)
  )

  const canBatchMove = computed(() =>
    selectedItems.value.every((item) => item.canManage)
  )

  // 方法
  const initialize = async () => {
    try {
      isLoading.value = true

      // 获取当前用户信息
      await getCurrentUser()

      // 获取系统配置
      await getSystemConfig()

      // 设置根目录
      await setCurrentDirectory(null)
    } catch (err) {
      error.value = err.message || '初始化失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const getCurrentUser = async () => {
    // TODO: 从API获取当前用户信息
    currentUser.value = {
      id: 1,
      name: '当前用户',
      avatar: '',
      permissions: 63, // 所有权限
    }
  }

  const getSystemConfig = async () => {
    // TODO: 从API获取系统配置
    config.value = {
      maxFileSize: 100 * 1024 * 1024,
      allowedFileTypes: [
        'image/*',
        'video/*',
        'audio/*',
        'application/pdf',
        'text/*',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ],
      uploadChunkSize: 2 * 1024 * 1024,
      previewEnabled: true,
      thumbnailEnabled: true,
    }
  }

  const setCurrentDirectory = async (directoryId) => {
    try {
      currentDirectoryId.value = directoryId

      if (directoryId) {
        // TODO: 从API获取目录信息
        currentDirectory.value = {
          id: directoryId,
          name: '示例目录',
          path: '/示例目录',
          parentId: null,
        }
      } else {
        currentDirectory.value = {
          id: null,
          name: '根目录',
          path: '/',
          parentId: null,
        }
      }

      // 更新面包屑
      await updateBreadcrumb()
    } catch (err) {
      error.value = err.message || '设置当前目录失败'
      throw err
    }
  }

  const updateBreadcrumb = async () => {
    const path = []

    // 添加根目录
    path.push({
      id: null,
      label: '根目录',
      to: '/',
    })

    if (currentDirectory.value && currentDirectory.value.id) {
      // TODO: 从API获取完整路径
      path.push({
        id: currentDirectory.value.id,
        label: currentDirectory.value.name,
        to: `/fms/directory/${currentDirectory.value.id}`,
      })
    }

    breadcrumbPath.value = path
  }

  const setSearchKeyword = (keyword) => {
    searchKeyword.value = keyword
  }

  const setViewMode = (mode) => {
    viewMode.value = mode
  }

  const selectItem = (item) => {
    const index = selectedItems.value.findIndex(
      (selected) => selected.id === item.id && selected.type === item.type
    )

    if (index === -1) {
      selectedItems.value.push(item)
    }
  }

  const unselectItem = (item) => {
    const index = selectedItems.value.findIndex(
      (selected) => selected.id === item.id && selected.type === item.type
    )

    if (index !== -1) {
      selectedItems.value.splice(index, 1)
    }
  }

  const toggleItemSelection = (item) => {
    const index = selectedItems.value.findIndex(
      (selected) => selected.id === item.id && selected.type === item.type
    )

    if (index === -1) {
      selectedItems.value.push(item)
    } else {
      selectedItems.value.splice(index, 1)
    }
  }

  const selectAll = (items) => {
    selectedItems.value = [...items]
  }

  const clearSelection = () => {
    selectedItems.value = []
  }

  const isItemSelected = (item) => {
    return selectedItems.value.some(
      (selected) => selected.id === item.id && selected.type === item.type
    )
  }

  const setLoading = (loading) => {
    isLoading.value = loading
  }

  const setError = (err) => {
    error.value = err
  }

  const clearError = () => {
    error.value = null
  }

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取文件图标
  const getFileIcon = (file) => {
    const ext = file.extension?.toLowerCase()
    const mimeType = file.mime_type?.toLowerCase()

    if (mimeType?.startsWith('image/')) return 'el-icon-picture'
    if (mimeType?.startsWith('video/')) return 'el-icon-video-camera'
    if (mimeType?.startsWith('audio/')) return 'el-icon-headset'
    if (mimeType === 'application/pdf') return 'el-icon-document'
    if (mimeType?.includes('word')) return 'el-icon-document'
    if (mimeType?.includes('excel')) return 'el-icon-s-grid'
    if (mimeType?.includes('powerpoint')) return 'el-icon-present'
    if (mimeType?.startsWith('text/')) return 'el-icon-document'
    if (ext === 'zip' || ext === 'rar' || ext === '7z')
      return 'el-icon-folder-opened'

    return 'el-icon-document'
  }

  // 检查文件是否可预览
  const canPreviewFile = (file) => {
    if (!config.value.previewEnabled) return false

    const mimeType = file.mime_type?.toLowerCase()

    return (
      mimeType?.startsWith('image/') ||
      mimeType?.startsWith('video/') ||
      mimeType?.startsWith('audio/') ||
      mimeType === 'application/pdf' ||
      mimeType?.startsWith('text/')
    )
  }

  return {
    // 状态
    currentDirectoryId,
    currentDirectory,
    breadcrumbPath,
    searchKeyword,
    viewMode,
    selectedItems,
    isLoading,
    error,
    currentUser,
    config,

    // 计算属性
    hasSelection,
    selectedDirectories,
    selectedFiles,
    canBatchDelete,
    canBatchMove,

    // 方法
    initialize,
    getCurrentUser,
    getSystemConfig,
    setCurrentDirectory,
    updateBreadcrumb,
    setSearchKeyword,
    setViewMode,
    selectItem,
    unselectItem,
    toggleItemSelection,
    selectAll,
    clearSelection,
    isItemSelected,
    setLoading,
    setError,
    clearError,
    formatFileSize,
    getFileIcon,
    canPreviewFile,
  }
})
