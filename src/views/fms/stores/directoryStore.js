/**
 * FMS 目录状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as directoryApi from '../../../api/fms/directories'

export const useDirectoryStore = defineStore('fmsDirectory', () => {
  // 状态
  const directories = ref([])
  const treeData = ref([])
  const isLoading = ref(false)
  const isTreeLoading = ref(false)
  const error = ref(null)
  const pagination = ref({
    total: 0,
    page: 1,
    pageSize: 20,
  })

  // 缓存
  const directoryCache = ref(new Map())
  const treeCache = ref(null)

  // 计算属性
  const directoryMap = computed(() => {
    const map = new Map()
    directories.value.forEach((dir) => {
      map.set(dir.id, dir)
    })
    return map
  })

  const rootDirectories = computed(() =>
    directories.value.filter((dir) => !dir.parentId)
  )

  const hasDirectories = computed(() => directories.value.length > 0)

  // 方法
  const fetchDirectories = async (params = {}) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await directoryApi.list(params)

      // 确保directories.value是一个数组
      directories.value = response.data?.data || []
      pagination.value = {
        total: response.total,
        page: response.page,
        pageSize: response.pageSize,
      }

      // 更新缓存
      directories.value.forEach((dir) => {
        directoryCache.value.set(dir.id, dir)
      })

      return response
    } catch (err) {
      error.value = err.message || '获取目录列表失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchDirectoryTree = async (params = {}) => {
    try {
      isTreeLoading.value = true
      error.value = null

      // 检查缓存
      if (treeCache.value && !params.refresh) {
        treeData.value = treeCache.value
        // return treeCache.value
      }

      const response = await directoryApi.tree(params)

      treeData.value = response.data || []
      treeCache.value = response.data || []

      return response
    } catch (err) {
      error.value = err.message || '获取目录树失败'
      throw err
    } finally {
      isTreeLoading.value = false
    }
  }

  const getDirectory = async (id, useCache = true) => {
    // 先从缓存获取
    if (directoryCache.value.has(id) && useCache) {
      return directoryCache.value.get(id)
    }

    try {
      const response = await directoryApi.overview(id)
      let directory = response.data
      directoryCache.value.set(id, directory)
      return directory
    } catch (err) {
      error.value = err.message || '获取目录信息失败'
      throw err
    }
  }

  const createDirectory = async (data) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await directoryApi.create(data)
      const newDirectory = response.data

      // 更新列表
      directories.value.unshift(newDirectory)

      // 更新缓存
      directoryCache.value.set(newDirectory.id, newDirectory)

      // 清除树缓存
      treeCache.value = null

      return newDirectory
    } catch (err) {
      error.value = err.message || '创建目录失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const renameDirectory = async (id, name) => {
    try {
      isLoading.value = true
      error.value = null

      await directoryApi.rename(id, { name })

      // 更新列表中的目录
      const index = directories.value.findIndex((dir) => dir.id === id)
      if (index !== -1) {
        directories.value[index].name = name
        directories.value[index].updatedAt = new Date().toISOString()
      }

      // 更新缓存
      if (directoryCache.value.has(id)) {
        const cached = directoryCache.value.get(id)
        cached.name = name
        cached.updatedAt = new Date().toISOString()
      }

      // 清除树缓存
      treeCache.value = null

      return true
    } catch (err) {
      error.value = err.message || '重命名目录失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateDirectory = async (id, data) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await directoryApi.update(id, data)
      const updatedDirectory = response.data

      // 更新列表中的目录
      const index = directories.value.findIndex((dir) => dir.id === id)
      if (index !== -1) {
        directories.value[index] = {
          ...directories.value[index],
          ...updatedDirectory,
        }
        directories.value[index].updatedAt = new Date().toISOString()
      }

      // 强制清除缓存，确保下次获取时是最新数据
      // 这解决了权限数据缓存不一致的问题
      directoryCache.value.delete(id)

      // 清除树缓存
      treeCache.value = null

      return updatedDirectory
    } catch (err) {
      error.value = err.message || '更新目录失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const moveDirectory = async (id, newParentId) => {
    try {
      isLoading.value = true
      error.value = null

      await directoryApi.move(id, { newParentId })

      // 更新列表中的目录
      const index = directories.value.findIndex((dir) => dir.id === id)
      if (index !== -1) {
        directories.value[index].parentId = newParentId
        directories.value[index].updatedAt = new Date().toISOString()
      }

      // 更新缓存
      if (directoryCache.value.has(id)) {
        const cached = directoryCache.value.get(id)
        cached.parentId = newParentId
        cached.updatedAt = new Date().toISOString()
      }

      // 清除树缓存
      treeCache.value = null

      return true
    } catch (err) {
      error.value = err.message || '移动目录失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteDirectory = async (id) => {
    try {
      isLoading.value = true
      error.value = null

      await directoryApi.destroy(id)

      // 从列表中移除
      const index = directories.value.findIndex((dir) => dir.id === id)
      if (index !== -1) {
        directories.value.splice(index, 1)
      }

      // 从缓存中移除
      directoryCache.value.delete(id)

      // 清除树缓存
      treeCache.value = null

      return true
    } catch (err) {
      error.value = err.message || '删除目录失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const refreshDirectories = async (params = {}) => {
    // 清除缓存
    directoryCache.value.clear()
    treeCache.value = null

    return await fetchDirectories(params)
  }

  const refreshTree = async (params = {}) => {
    treeCache.value = null
    return await fetchDirectoryTree({ ...params, refresh: true })
  }

  const clearCache = () => {
    directoryCache.value.clear()
    treeCache.value = null
  }

  const setLoading = (loading) => {
    isLoading.value = loading
  }

  const setError = (err) => {
    error.value = err
  }

  const clearError = () => {
    error.value = null
  }

  // 工具方法
  const findDirectoryPath = (directoryId) => {
    const path = []
    let current = directoryMap.value.get(directoryId)

    while (current) {
      path.unshift(current)
      current = current.parentId
        ? directoryMap.value.get(current.parentId)
        : null
    }

    return path
  }

  // 获取目录完整路径，用于面包屑导航
  const getDirectoryPath = async (directoryId) => {
    if (!directoryId) return []

    // 辅助函数：从树形数据中查找目录及其路径
    const findInTree = (nodes, targetId, currentPath = []) => {
      for (const node of nodes) {
        if (node.id === targetId || String(node.id) === String(targetId)) {
          return [...currentPath, { name: node.name, id: node.id }]
        }
        if (node.children && node.children.length > 0) {
          const result = findInTree(node.children, targetId, [
            ...currentPath,
            { name: node.name, id: node.id },
          ])
          if (result) return result
        }
      }
      return null
    }

    // 首先尝试从树形数据中查找完整路径
    if (treeData.value && treeData.value.length > 0) {
      const pathFromTree = findInTree(treeData.value, directoryId)
      if (pathFromTree) {
        return pathFromTree
      }
    }

    // 若树形数据中没有，尝试从缓存构建路径
    const buildPathFromCache = (id) => {
      const path = []
      let currentId = id
      const visited = new Set() // 防止循环引用

      while (currentId) {
        if (visited.has(currentId)) {
          console.warn('Circular reference detected in directory path')
          break
        }
        visited.add(currentId)

        let directory = directoryCache.value.get(currentId)
        if (!directory) {
          directory = directories.value.find(
            (dir) =>
              dir.id === currentId || String(dir.id) === String(currentId)
          )
        }

        if (!directory) {
          // 如果找不到目录信息，尝试返回部分路径
          if (path.length > 0) {
            return path.reverse()
          }
          return []
        }

        path.push({ name: directory.name, id: directory.id })
        currentId = directory.parentId
      }

      return path.reverse()
    }

    return buildPathFromCache(directoryId)
  }

  const getChildDirectories = (parentId) => {
    return directories.value.filter((dir) => dir.parentId === parentId)
  }

  const hasChildDirectories = (parentId) => {
    return directories.value.some((dir) => dir.parentId === parentId)
  }

  return {
    // 状态
    directories,
    treeData,
    isLoading,
    isTreeLoading,
    error,
    pagination,

    // 计算属性
    directoryMap,
    rootDirectories,
    hasDirectories,

    // 方法
    fetchDirectories,
    fetchDirectoryTree,
    getDirectory,
    createDirectory,
    renameDirectory,
    updateDirectory,
    moveDirectory,
    deleteDirectory,
    refreshDirectories,
    refreshTree,
    clearCache,
    setLoading,
    setError,
    clearError,

    // 工具方法
    findDirectoryPath,
    getDirectoryPath,
    getChildDirectories,
    hasChildDirectories,
  }
})
