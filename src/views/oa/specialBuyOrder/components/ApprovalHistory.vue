<template>
  <fireflyDialog
    v-model="dialogVisible"
    title="审批历史记录"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="approval-history">
      <div v-if="groupedApprovalHistory.length === 0" class="no-history">
        暂无审批历史记录
      </div>

      <div v-else>
        <div
          v-for="group in groupedApprovalHistory"
          :key="group.round"
          class="history-round"
        >
          <div class="round-header">
            <h4>
              第{{ group.round }}轮{{ group.round === 1 ? '提交' : '重新提交' }}
            </h4>
          </div>

          <div class="round-content">
            <div
              v-for="approval in group.approvals"
              :key="approval.id"
              class="history-item"
            >
              <div class="history-step">
                <div class="step-info">
                  <span class="step-title">{{ approval.step_name }}</span>
                  <span class="step-time">
                    {{ approval.approved_at || approval.created_at }}
                  </span>
                </div>

                <div class="step-details">
                  <div class="approver-info">
                    <span class="approver-name">
                      {{ approval.approver_name }}
                    </span>
                    <span
                      v-if="approval.approver_department"
                      class="approver-dept"
                    >
                      ({{ approval.approver_department }})
                    </span>
                  </div>

                  <div class="action-info">
                    <span
                      class="action-status"
                      :class="{
                        'status-approved': approval.action === 'approve',
                        'status-rejected': approval.action === 'reject',
                        'status-returned': approval.action === 'returned',
                        'status-pending': approval.action === 'pending',
                        'status-cancelled': approval.action === 'cancelled',
                        'status-submitted': approval.action === 'submitted',
                        'status-resubmitted': approval.action === 'resubmitted',
                      }"
                    >
                      {{ getActionText(approval.action) }}
                    </span>

                    <span v-if="approval.comment" class="action-comment">
                      {{ approval.comment }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </fireflyDialog>
</template>

<script setup>
  import { computed } from 'vue'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    approvalHistory: {
      type: Array,
      default: () => [],
    },
  })

  const emit = defineEmits(['close'])

  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => {
      if (!val) {
        emit('close')
      }
    },
  })

  // 按轮次分组审批历史
  const groupedApprovalHistory = computed(() => {
    if (!props.approvalHistory || props.approvalHistory.length === 0) {
      return []
    }

    // 按轮次分组
    const groups = {}
    props.approvalHistory.forEach((approval) => {
      const round = approval.round || 1
      if (!groups[round]) {
        groups[round] = []
      }
      groups[round].push(approval)
    })

    // 转换为数组并按轮次排序（最新的在前）
    return Object.keys(groups)
      .map((round) => ({
        round: parseInt(round),
        approvals: groups[round].sort(
          (a, b) => new Date(a.created_at) - new Date(b.created_at)
        ),
      }))
      .sort((a, b) => b.round - a.round)
  })

  // 获取操作文本
  const getActionText = (action) => {
    const actionMap = {
      approve: '同意',
      reject: '驳回',
      returned: '退回',
      pending: '等待审批',
      cancelled: '取消',
      submitted: '已提交',
      resubmitted: '重新提交',
    }
    return actionMap[action] || action
  }

  const handleClose = () => {
    emit('close')
  }
</script>

<style lang="scss" scoped>
  // 审批历史样式
  .approval-history {
    max-height: 500px;
    overflow-y: auto;

    .no-history {
      text-align: center;
      color: #999;
      padding: 40px 0;
    }

    .history-round {
      margin-bottom: 30px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;

      .round-header {
        background: #f5f7fa;
        padding: 12px 16px;
        border-bottom: 1px solid #e4e7ed;

        h4 {
          margin: 0;
          color: #303133;
          font-size: 14px;
          font-weight: 600;
        }
      }

      .round-content {
        padding: 16px;
      }

      .history-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .history-step {
          border-left: 3px solid #e4e7ed;
          padding-left: 16px;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 8px;
            width: 9px;
            height: 9px;
            border-radius: 50%;
            background: #e4e7ed;
          }

          .step-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .step-title {
              font-weight: 600;
              color: #303133;
            }

            .step-time {
              color: #909399;
              font-size: 12px;
            }
          }

          .step-details {
            .approver-info {
              margin-bottom: 4px;

              .approver-name {
                color: #606266;
                font-weight: 500;
              }

              .approver-dept {
                color: #909399;
                font-size: 12px;
                margin-left: 4px;
              }
            }

            .action-info {
              .action-status {
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                margin-right: 8px;

                &.status-approved {
                  background: #f0f9ff;
                  color: #18be6f;
                  border: 1px solid #b3e19d;
                }

                &.status-rejected {
                  background: #fef0f0;
                  color: #ff5e4b;
                  border: 1px solid #fbc4c4;
                }

                &.status-returned {
                  background: #fdf6ec;
                  color: #e6a23c;
                  border: 1px solid #f5dab1;
                }

                &.status-pending {
                  background: #f4f4f5;
                  color: #909399;
                  border: 1px solid #d3d4d6;
                }

                &.status-cancelled {
                  background: #fef0f0;
                  color: #ff5e4b;
                  border: 1px solid #fbc4c4;
                }

                &.status-submitted {
                  background: #f0f9ff;
                  color: #409eff;
                  border: 1px solid #b3d8ff;
                }

                &.status-resubmitted {
                  background: #f0f9ff;
                  color: #409eff;
                  border: 1px solid #b3d8ff;
                }
              }

              .action-comment {
                color: #606266;
                font-size: 13px;
              }
            }
          }
        }
      }
    }
  }
</style>
