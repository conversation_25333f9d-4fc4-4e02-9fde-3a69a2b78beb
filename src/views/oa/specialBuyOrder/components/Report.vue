<template>
  <firefly-dialog
    v-model="visible"
    :show-default-button="false"
    :show-close="false"
    :close-on-click-modal="true"
    width="1080px"
    :max-height="'90vh'"
    custom-class="iqc-report-dialog"
  >
    <!-- 该HTML内容为在EXCEL中导出为HTML后，修改而来 -->
    <div id="iqc-report-container" style="padding: 10px 10px 0 10px">
      <table
        width="1007"
        border="0"
        cellpadding="0"
        cellspacing="0"
        style="width: 755.25pt; border-collapse: collapse; table-layout: fixed"
      >
        <tr height="40" class="xl65" style="height: 30pt; mso-height-alt: 600">
          <td
            class="xl70"
            height="40"
            width="1007"
            colspan="8"
            style="
              height: 30pt;
              width: 755.25pt;
              border-right: none;
              border-bottom: none;
            "
          >
            <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
            中山市天启智能科技有限公司
          </td>
        </tr>
        <tr
          height="33.33"
          class="xl65"
          style="height: 25pt; mso-height-alt: 500"
        >
          <td
            class="xl71"
            height="33.33"
            colspan="8"
            style="
              height: 25pt;
              border-right: none;
              border-bottom: none;
              position: relative;
            "
          >
            <img
              src="@/assets/logo2.png"
              alt="logo"
              style="
                position: absolute;
                width: 70px;
                height: auto;
                left: 270px;
                top: 50%;
                transform: translateY(-50%);
              "
            />
            <span>&nbsp;</span>
            地址：中山市东区中山四路57号宏宇大厦1座2101室
            <br />
            电话：0760-89881218
            <span>&nbsp;&nbsp;</span>
            传真：0760-89881219
          </td>
        </tr>
        <tr height="58.67" style="height: 44pt; mso-height-alt: 880">
          <td
            class="xl72"
            height="58.67"
            colspan="8"
            style="height: 44pt; border-right: none; border-bottom: none"
          >
            特采申请单
          </td>
        </tr>
        <tr height="48" style="height: 36pt; mso-height-alt: 720">
          <td class="xl73" height="48" style="height: 36pt">料号</td>
          <td class="xl74" style="white-space: normal; word-break: break-all">
            {{ getQcOrderField('prod_code') || '' }}
          </td>
          <td class="xl73">名称</td>
          <td class="xl75" colspan="3">
            {{ getQcOrderField('prod_name') || '' }}
          </td>
          <td class="xl73">供应商</td>
          <td class="xl97">{{ getSupplierName() }}</td>
        </tr>
        <tr height="48" style="height: 36pt; mso-height-alt: 720">
          <td class="xl73" height="48" style="height: 36pt">规格</td>
          <td class="xl75" colspan="7">
            {{ getQcOrderField('prod_spec') || '' }}
          </td>
        </tr>
        <tr height="57.33" style="height: 43pt; mso-height-alt: 860">
          <td class="xl73" height="57.33" style="height: 43pt">订单号</td>
          <td class="xl76" style="white-space: normal; word-break: break-all">
            {{ rowData?.order_no || '' }}
          </td>
          <td class="xl73">回货数量</td>
          <td class="xl77">{{ getQcOrderField('num') || '' }}</td>
          <td class="xl73">抽检数量</td>
          <td class="xl98">{{ getQcOrderField('examine_num') || '' }}</td>
          <td class="xl73">收货日期</td>
          <td class="xl97">
            {{ formatDate(getQcOrderField('created_at')) || '' }}
          </td>
        </tr>
        <tr height="57.33" style="height: 43pt; mso-height-alt: 860">
          <td class="xl73" height="57.33" style="height: 43pt">产品分类</td>
          <td class="xl78">{{ getProductCategoryName() }}</td>
          <td class="xl73">不良数量</td>
          <td class="xl79">{{ getQcOrderField('defective_num') || 0 }}</td>
          <td class="xl73">不良率</td>
          <td class="xl99">{{ calculateDefectiveRate() }}</td>
          <td class="xl73">检验日期</td>
          <td class="xl97">
            {{ formatDate(getQcOrderField('status_change_at')) || '' }}
          </td>
        </tr>
        <tr height="52" style="height: 39pt; mso-height-alt: 780">
          <td class="xl73" height="52" style="height: 39pt">异常内容</td>
          <td
            class="xl80"
            colspan="7"
            style="
              border-right: 0.5pt solid windowtext;
              border-bottom: 0.5pt solid windowtext;
            "
          >
            {{ formatAbnormalContent() }}
          </td>
        </tr>
        <tr height="61.33" style="height: 46pt; mso-height-alt: 920">
          <td class="xl82" height="61.33" style="height: 46pt">特采原因</td>
          <td
            class="xl78"
            colspan="7"
            style="border-right: 0.5pt solid windowtext"
          >
            {{ rowData?.special_reason || '' }}
          </td>
        </tr>
        <tr height="56" class="xl66" style="height: 42pt; mso-height-alt: 840">
          <td class="xl82" height="56" style="height: 42pt">特采详情</td>
          <td
            class="xl78"
            colspan="7"
            style="border-right: 0.5pt solid windowtext"
          >
            {{ rowData?.special_details || rowData?.details || '' }}
          </td>
        </tr>
        <tr height="56" class="xl66" style="height: 42pt; mso-height-alt: 840">
          <td class="xl82" height="56" style="height: 42pt">特采范围</td>
          <td
            class="xl78"
            colspan="7"
            style="border-right: 0.5pt solid windowtext"
          >
            {{ rowData?.special_scope || '' }}
          </td>
        </tr>
        <!-- 动态渲染订单信息 -->
        <tr
          v-for="order in getAllOrdersForDisplay()"
          :key="`order-${order.type}-${order.id}`"
          height="56"
          class="xl66"
          style="height: 42pt; mso-height-alt: 840"
        >
          <td
            class="xl82"
            height="56"
            style="height: 42pt; white-space: normal; word-break: break-all"
          >
            {{ order.label }} ：{{ order.code }}
          </td>
          <td
            class="xl78"
            colspan="7"
            style="
              border-right: 0.5pt solid windowtext;
              white-space: normal;
              word-break: break-all;
            "
          >
            {{ order.content }}
          </td>
        </tr>
        <tr height="64" style="height: 48pt; mso-height-alt: 960">
          <td class="xl73" height="64" style="height: 48pt">申请时间</td>
          <td
            class="xl74"
            colspan="3"
            style="
              border-right: none;
              border-bottom: 0.5pt solid windowtext;
              text-align: left;
            "
          >
            {{ formatDate(rowData?.created_at) || '' }}
          </td>
          <td class="xl73">申请人</td>
          <td
            class="xl85"
            colspan="3"
            style="
              border-right: 0.5pt solid windowtext;
              border-bottom: 0.5pt solid windowtext;
              text-align: left;
            "
          >
            {{ rowData?.creator_name || rowData?.applicant_name || '' }}
          </td>
        </tr>
        <tr height="61.33" style="height: 46pt; mso-height-alt: 920">
          <td class="xl73" height="61.33" style="height: 46pt">判定结果</td>
          <td
            class="xl86"
            colspan="7"
            style="
              border-right: 0.5pt solid windowtext;
              border-bottom: 0.5pt solid windowtext;
            "
          >
            {{ getResultCheckbox(1) }}特采使用
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            {{ getResultCheckbox(4) }}返工
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            {{ getResultCheckbox(2) }}挑选
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            {{ getResultCheckbox(3) }}让步接收
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            {{ getResultCheckbox(5) }}退货
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            □其他
          </td>
        </tr>
        <tr
          height="30.67"
          class="xl67"
          style="height: 23pt; mso-height-alt: 460"
        >
          <td class="xl88" height="30.67" style="height: 23pt">序号</td>
          <td class="xl89">部门</td>
          <td
            class="xl90"
            colspan="2"
            style="border-right: none; border-bottom: 0.5pt solid windowtext"
          >
            会签
          </td>
          <td
            class="xl90"
            colspan="3"
            style="border-right: none; border-bottom: 0.5pt solid windowtext"
          >
            意见
          </td>
          <td
            class="xl90"
            style="
              border-right: 0.5pt solid windowtext;
              border-bottom: 0.5pt solid windowtext;
            "
          >
            审核时间
          </td>
        </tr>
        <tr
          height="49.33"
          class="xl67"
          style="height: 37pt; mso-height-alt: 740"
        >
          <td class="xl88" height="49.33" style="height: 43pt" x:num>1</td>
          <td class="xl89">采购经理</td>
          <td
            class="xl93"
            colspan="2"
            style="border-right: none; border-bottom: 0.5pt solid windowtext"
          >
            {{ getApproverName(APPROVAL_STEPS.PURCHASE) }}
          </td>
          <td
            class="xl108"
            colspan="3"
            style="border-right: none; border-bottom: 0.5pt solid windowtext"
          >
            {{ getApprovalComment(APPROVAL_STEPS.PURCHASE) }}
          </td>
          <td
            class="xl108"
            style="
              border-right: 0.5pt solid #000000;
              border-bottom: 0.5pt solid windowtext;
            "
          >
            {{ getApprovalTime(APPROVAL_STEPS.PURCHASE) }}
          </td>
        </tr>
        <tr
          height="57.33"
          class="xl67"
          style="height: 43pt; mso-height-alt: 860"
        >
          <td class="xl92" height="57.33" style="height: 43pt" x:num>2</td>
          <td class="xl89">品质经理</td>
          <td
            class="xl93"
            colspan="2"
            style="border-right: none; border-bottom: 0.5pt solid windowtext"
          >
            {{ getApproverName(APPROVAL_STEPS.QUALITY) }}
          </td>
          <td
            class="xl108"
            colspan="3"
            style="border-right: none; border-bottom: 0.5pt solid windowtext"
          >
            {{ getApprovalComment(APPROVAL_STEPS.QUALITY) }}
          </td>
          <td
            class="xl108"
            style="
              border-right: 0.5pt solid #000000;
              border-bottom: 0.5pt solid windowtext;
            "
          >
            {{ getApprovalTime(APPROVAL_STEPS.QUALITY) }}
          </td>
        </tr>
        <tr
          height="57.33"
          class="xl67"
          style="height: 43pt; mso-height-alt: 860"
        >
          <td class="xl92" height="57.33" style="height: 43pt" x:num>3</td>
          <td class="xl89">副总经理</td>
          <td
            class="xl93"
            colspan="2"
            style="border-right: none; border-bottom: 0.5pt solid windowtext"
          >
            {{ getApproverName(APPROVAL_STEPS.MANAGER) }}
          </td>
          <td
            class="xl108"
            colspan="3"
            style="border-right: none; border-bottom: 0.5pt solid windowtext"
          >
            {{ getApprovalComment(APPROVAL_STEPS.MANAGER) }}
          </td>
          <td
            class="xl108"
            style="
              border-right: 0.5pt solid #000000;
              border-bottom: 0.5pt solid windowtext;
            "
          >
            {{ getApprovalTime(APPROVAL_STEPS.MANAGER) }}
          </td>
        </tr>
        <tr class="xl67"></tr>
        <tr class="xl67"></tr>
        <tr height="20" style="height: 15pt">
          <td class="xl68" height="20" style="height: 15pt"></td>
          <td class="xl66"></td>
          <td class="xl69"></td>
          <td class="xl66"></td>
          <td class="xl69"></td>
          <td class="xl66"></td>
          <td class="xl111">表单编号：TQ-QA-006</td>
        </tr>
      </table>
    </div>
    <div style="float: right; margin-top: 15px; margin-right: 15px">
      <el-button type="primary" @click="getPDF()">导出为PDF</el-button>
      <el-button type="primary" @click="closeReport()">关闭</el-button>
    </div>
  </firefly-dialog>
</template>

<script setup>
  import { ref, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import { htmlToPDF } from '@/utils/html2pdf'
  import { getErpSupplierInfo, getErpProductList } from '@/api/oaQcerp'
  import { overView } from '@/api/oaQc'
  import { overView as productionOrderOverView } from '@/api/productionOrder'
  import { overView as assembleOrderOverView } from '@/api/assembleOrder'

  const visible = ref(false)
  const rowData = ref(null)
  const supplierInfo = ref(null)
  const pmpcCategoryList = ref([])
  const qcOrderData = ref(null)
  const productionOrdersData = ref([])
  const assembleOrdersData = ref([])

  // 审批步骤常量
  const APPROVAL_STEPS = {
    PURCHASE: 1, // 采购经理
    QUALITY: 2, // 品质经理
    MANAGER: 4, // 副总经理
  }

  // 获取指定步骤的审批信息（最新轮次）
  const getApprovalInfo = (step) => {
    if (!rowData.value?.approvals || !Array.isArray(rowData.value.approvals)) {
      return null
    }

    // 查找指定步骤的所有审批记录
    const stepApprovals = rowData.value.approvals.filter(
      (approval) => approval.step === step
    )
    if (stepApprovals.length === 0) return null

    // 获取最新轮次号
    const maxRound = Math.max(
      ...stepApprovals.map((approval) => approval.round)
    )

    // 获取最新轮次的审批记录
    const latestRoundApprovals = stepApprovals.filter(
      (approval) => approval.round === maxRound
    )

    // 在最新轮次中，优先返回已审批的记录，如果没有则返回pending记录
    const approvedRecord = latestRoundApprovals.find(
      (approval) => approval.action !== 'pending'
    )
    return approvedRecord || latestRoundApprovals[0]
  }

  // 获取审批人姓名
  const getApproverName = (step) => {
    const approval = getApprovalInfo(step)
    return approval?.approver_name || ''
  }

  // 获取审批意见
  const getApprovalComment = (step) => {
    const approval = getApprovalInfo(step)
    if (!approval || approval.action === 'pending') return ''

    const actionText =
      {
        approve: '同意',
        reject: '驳回',
        returned: '退回',
      }[approval.action] || approval.action

    return approval.comment ? `${approval.comment}` : actionText
  }

  // 获取审核时间
  const getApprovalTime = (step) => {
    const approval = getApprovalInfo(step)
    if (!approval || approval.action === 'pending') return ''

    // 格式化时间显示
    if (approval.updated_at) {
      const date = new Date(approval.updated_at)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
    }

    return ''
  }

  // 格式化异常内容显示
  const formatAbnormalContent = () => {
    const appearanceRemark = getQcOrderField('appearance_inspection_remark')
    const functionRemark = getQcOrderField('function_inspection_remark')

    const parts = []

    if (appearanceRemark && appearanceRemark.trim()) {
      parts.push(`外观：${appearanceRemark.trim()}`)
    }

    if (functionRemark && functionRemark.trim()) {
      parts.push(`功能：${functionRemark.trim()}`)
    }

    return parts.join(' ')
  }

  // 格式化产品信息显示
  const formatProductInfo = () => {
    const parts = []

    // 产品料号
    const productCode =
      rowData.value?.product_code || rowData.value?.prod_code || 'xxx'
    parts.push(`产品料号：${productCode}`)

    // 产品名称
    const productName =
      rowData.value?.product_name || rowData.value?.prod_name || 'xxx'
    parts.push(`产品名称：${productName}`)

    // 委外加工厂
    const supplier =
      rowData.value?.supplier_name || rowData.value?.supply_name || 'xxx'
    parts.push(`委外加工厂：${supplier}`)

    // 数量
    const quantity = rowData.value?.quantity || rowData.value?.qty || 'xxx'
    parts.push(`数量：${quantity}`)

    return parts.join(' ')
  }

  // 获取所有订单数据用于显示
  const getAllOrdersForDisplay = () => {
    const orders = []

    // 处理生产订单
    if (productionOrdersData.value && productionOrdersData.value.length > 0) {
      productionOrdersData.value.forEach((order) => {
        if (order.data && order.data.code) {
          orders.push({
            type: 'production',
            id: order.id,
            label: '生产订单',
            code: order.data.code,
            content: formatOrderContent(order.data, 'production'),
          })
        }
      })
    }

    // 处理组装订单
    if (assembleOrdersData.value && assembleOrdersData.value.length > 0) {
      assembleOrdersData.value.forEach((order) => {
        if (order.data && order.data.code) {
          orders.push({
            type: 'assemble',
            id: order.id,
            label: '组装订单',
            code: order.data.code,
            content: formatOrderContent(order.data, 'assemble'),
          })
        }
      })
    }

    return orders
  }

  // 订单字段配置
  const ORDER_FIELD_CONFIG = {
    production: {
      产品名称: 'product_name',
      数量: 'woqty',
      料号: 'product_code',
      委外加工厂: 'factory_name',
    },
    assemble: {
      产品名称: 'product_name',
      数量: 'num',
      料号: 'product_code',
      委外加工厂: '-', // 固定值
    },
  }

  // 格式化订单内容显示
  const formatOrderContent = (orderData, orderType) => {
    const config = ORDER_FIELD_CONFIG[orderType]
    if (!config) return ''

    return Object.entries(config)
      .map(([label, field]) => {
        const value = field === '-' ? '-' : orderData?.[field] || ''
        return value ? `${label}：${value}` : ''
      })
      .filter(Boolean)
      .join(';  ')
  }

  // 生成空格
  const generateSpace = (count) => '\u00A0'.repeat(count)
  // 生成下划线
  const generateUnderLine = (count) => '_'.repeat(count)

  const showReport = async (data) => {
    console.log('showReport', data)
    rowData.value = data
    visible.value = true

    // 先获取QC订单详细信息
    await loadQcOrderData()

    await Promise.all([
      loadSupplierInfo(),
      loadProductCategoryList(),
      loadOrdersData(),
    ])
  }

  // 获取QC订单详细信息
  const loadQcOrderData = async () => {
    try {
      const qcOrderId = rowData.value?.qc_order?.id || rowData.value?.id
      if (qcOrderId) {
        const response = await overView({ id: qcOrderId })
        qcOrderData.value = response.data
        console.log('QC订单详细信息:', qcOrderData.value)
      }
    } catch (error) {
      console.error('获取QC订单详细信息失败:', error)
      qcOrderData.value = null
    }
  }

  // 获取供应商信息
  const loadSupplierInfo = async () => {
    try {
      const orderFactoryCode = getQcOrderField('order_factory_code')
      if (orderFactoryCode) {
        const response = await getErpSupplierInfo({
          order_factory_code: orderFactoryCode,
        })
        supplierInfo.value = response.data
      }
    } catch (error) {
      console.error('获取供应商信息失败:', error)
      supplierInfo.value = null
    }
  }

  // 获取产品分类列表
  const loadProductCategoryList = async () => {
    try {
      const response = await getErpProductList()
      pmpcCategoryList.value = response.data
    } catch (error) {
      console.error('获取产品分类列表失败:', error)
      pmpcCategoryList.value = []
    }
  }

  // 获取订单数据（生产订单和组装订单）
  const loadOrdersData = async () => {
    try {
      // 重置订单数据
      productionOrdersData.value = []
      assembleOrdersData.value = []

      // 获取订单ID数组
      const productionOrderIds = qcOrderData.value?.production_order_id || []
      const assembleOrderIds = qcOrderData.value?.assemble_order_id || []

      // 创建API调用Promise数组
      const apiCalls = []

      // 处理生产订单ID数组
      if (Array.isArray(productionOrderIds) && productionOrderIds.length > 0) {
        productionOrderIds.forEach((id) => {
          if (id) {
            apiCalls.push(
              productionOrderOverView({ id })
                .then((response) => ({
                  type: 'production',
                  id,
                  data: response.data,
                }))
                .catch((error) => {
                  console.error(`获取生产订单${id}失败:`, error)
                  return {
                    type: 'production',
                    id,
                    data: null,
                    error,
                  }
                })
            )
          }
        })
      }

      // 处理组装订单ID数组
      if (Array.isArray(assembleOrderIds) && assembleOrderIds.length > 0) {
        assembleOrderIds.forEach((id) => {
          if (id) {
            apiCalls.push(
              assembleOrderOverView({ id })
                .then((response) => ({
                  type: 'assemble',
                  id,
                  data: response.data,
                }))
                .catch((error) => {
                  console.error(`获取组装订单${id}失败:`, error)
                  return {
                    type: 'assemble',
                    id,
                    data: null,
                    error,
                  }
                })
            )
          }
        })
      }

      // 并行执行所有API调用
      if (apiCalls.length > 0) {
        const results = await Promise.all(apiCalls)

        // 分类存储结果
        results.forEach((result) => {
          if (result.type === 'production') {
            productionOrdersData.value.push(result)
          } else if (result.type === 'assemble') {
            assembleOrdersData.value.push(result)
          }
        })
      } else {
        console.log('没有找到订单ID，跳过订单数据获取')
      }
    } catch (error) {
      console.error('获取订单数据失败:', error)
      productionOrdersData.value = []
      assembleOrdersData.value = []
    }
  }

  // 格式化日期
  const formatDate = (dateStr) => {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
  }

  // 统一的QC订单数据获取方法
  const getQcOrderField = (fieldName) => {
    // 优先使用API获取的数据，fallback到原始数据
    return (
      qcOrderData.value?.[fieldName] ||
      rowData.value?.qc_order?.[fieldName] ||
      rowData.value?.[fieldName]
    )
  }

  // 计算不良率
  const calculateDefectiveRate = () => {
    const defectiveNum = getQcOrderField('defective_num') || 0
    const examineNum = getQcOrderField('examine_num') || 0

    if (examineNum === 0) return '0%'

    const rate = ((defectiveNum / examineNum) * 100).toFixed(2)
    return `${rate}%`
  }

  // 获取供应商名称
  const getSupplierName = () => {
    // 优先使用supply_name字段
    const supplyName = getQcOrderField('supply_name')
    if (supplyName) {
      return supplyName
    }

    // 其次使用从API获取的供应商信息
    if (supplierInfo.value?.NAME) {
      return supplierInfo.value.NAME
    }

    // 最后使用factory_name作为备用
    return getQcOrderField('factory_name') || ''
  }

  // 获取判定结果方块显示
  const getResultCheckbox = (value) => {
    const handleValue = getQcOrderField('unqualified_handle')
    return handleValue === value ? '■' : '□'
  }

  // 递归查找产品分类
  const findCategoryById = (categories, targetId) => {
    for (const category of categories) {
      // 检查当前项目的ID
      if (category.ID == targetId || category.id == targetId) {
        return category
      }
      // 如果有children，递归查找
      if (category.children && category.children.length > 0) {
        const found = findCategoryById(category.children, targetId)
        if (found) {
          return found
        }
      }
    }
    return null
  }

  // 获取产品分类名称
  const getProductCategoryName = () => {
    // 如果有产品分类ID，从列表中查找对应名称
    const categoryId = getQcOrderField('prod_pmpc')

    if (categoryId && pmpcCategoryList.value.length > 0) {
      const category = findCategoryById(pmpcCategoryList.value, categoryId)
      return category?.NAME || category?.text || ''
    }

    return ''
  }

  // 生成PDF
  const getPDF = async () => {
    try {
      // 修改PDF文件名为：订单号 + 特采申请单
      const orderNo = rowData.value?.order_no || '未知订单'
      const pdfName = `${orderNo}-特采申请单`

      // 确保DOM完全渲染
      await nextTick()

      // 临时移除可能影响渲染的样式
      const container = document.getElementById('iqc-report-container')
      if (container) {
        // 添加PDF导出专用样式
        container.style.backgroundColor = '#ffffff'
        container.style.color = '#000000'

        // 等待样式应用
        await new Promise((resolve) => setTimeout(resolve, 100))

        await htmlToPDF('iqc-report-container', pdfName, true)

        // 恢复原始样式
        container.style.backgroundColor = ''
        container.style.color = ''
      }
    } catch (error) {
      console.error('PDF导出失败:', error)
      ElMessage.error('PDF导出失败，请重试')
    }

    //原生调用打印来保存pdf,但是这样样式会有些问题
    // let oldBody = document.body.innerHTML // 保存
    // document.body.innerHTML = document.getElementById(
    //   'iqc-report-container'
    // ).innerHTML
    // window.print()
    // document.body.innerHTML = oldBody // 恢复
  }

  const closeReport = () => {
    visible.value = false
  }

  // Expose methods to parent component
  defineExpose({
    showReport,
  })
</script>

<style scoped>
  :deep(.iqc-report-dialog) {
    overflow-y: auto; /* 启用滚动 */
  }
  .iqc-report {
    width: 686.1pt;
    border-collapse: collapse;
    table-layout: fixed;
    margin: 0 auto;
    color: #000;
    position: relative;
  }
  td {
    padding-top: 1px;
    padding-right: 1px;
    padding-left: 1px;
    mso-ignore: padding;
    mso-number-format: 'General';
    text-align: general;
    vertical-align: middle;
    white-space: nowrap;
    mso-rotate: 0;
    mso-pattern: auto;
    mso-background-source: auto;
    color: #000000;
    font-size: 11pt;
    font-weight: 400;
    font-style: normal;
    text-decoration: none;
    font-family: 宋体;
    mso-generic-font-family: auto;

    border: none;
    mso-protection: locked visible;
  }
  .xl65 {
    vertical-align: bottom;
    background: #ffffff;
  }
  .xl66 {
    background: #ffffff;
  }
  .xl67 {
    background: #ffffff;
    color: windowtext;
    font-size: 12pt;
  }
  .xl68 {
    text-align: center;
    white-space: normal;
    background: #ffffff;
  }
  .xl69 {
    white-space: normal;
    background: #ffffff;
  }
  .xl70 {
    text-align: center;
    white-space: normal;
    background: #ffffff;
    font-size: 18pt;
  }
  .xl71 {
    text-align: center;
    white-space: normal;
    background: #ffffff;
    font-size: 8pt;
  }
  .xl72 {
    text-align: center;
    white-space: normal;
    background: #ffffff;
    font-size: 20pt;
  }
  .xl73 {
    text-align: center;
    white-space: normal;
    background: #ffffff;

    border: 0.5pt solid windowtext;
  }
  .xl74 {
    white-space: normal;
    background: #ffffff;

    border: 0.5pt solid windowtext;
  }
  .xl75 {
    white-space: normal;
    background: #ffffff;

    border: 0.5pt solid windowtext;
  }
  .xl76 {
    background: #ffffff;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl77 {
    white-space: normal;

    background: #ffffff;

    border-top: 0.5pt solid windowtext;
    border-right: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl78 {
    text-align: left;

    background: #ffffff;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl79 {
    text-align: left;

    background: #ffffff;

    border-top: 0.5pt solid windowtext;
    border-right: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl80 {
    text-align: left;
    white-space: normal;

    background: #ffffff;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl82 {
    text-align: center;
    white-space: normal;

    background: #ffffff;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-right: 0.5pt solid windowtext;
  }
  .xl83 {
    text-align: center;

    background: #ffffff;

    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl84 {
    text-align: center;

    background: #ffffff;

    border: 0.5pt solid windowtext;
  }
  .xl85 {
    text-align: center;

    background: #ffffff;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl86 {
    text-align: center;
    white-space: normal;

    background: #ffffff;
    color: windowtext;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl88 {
    text-align: center;

    background: #ffffff;
    color: windowtext;

    border-left: 1pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-right: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl89 {
    background: #ffffff;
    color: windowtext;

    border: 0.5pt solid windowtext;
    text-align: center;
  }
  .xl90 {
    text-align: center;

    background: #ffffff;
    color: windowtext;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl91 {
    text-align: center;

    background: #ffffff;
    color: windowtext;

    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl92 {
    text-align: center;

    background: #ffffff;
    color: windowtext;

    border-left: 1pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-right: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl93 {
    text-align: center;

    background: #ffffff;
    color: windowtext;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl95 {
    text-align: left;
    white-space: normal;

    background: #ffffff;
    color: windowtext;

    border: 0.5pt solid windowtext;
  }
  .xl97 {
    white-space: normal;

    background: #ffffff;

    border: 0.5pt solid windowtext;
  }
  .xl98 {
    white-space: normal;

    background: #ffffff;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl99 {
    mso-number-format: '0%';
    text-align: left;

    background: #ffffff;

    border-left: 0.5pt solid windowtext;
    border-top: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl101 {
    text-align: center;

    background: #ffffff;

    border-top: 0.5pt solid windowtext;
    border-right: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl107 {
    text-align: center;

    background: #ffffff;
    color: windowtext;

    border-top: 0.5pt solid windowtext;
    border-right: 0.5pt solid windowtext;
    border-bottom: 0.5pt solid windowtext;
  }
  .xl108 {
    text-align: center;
    white-space: normal;

    background: #ffffff;
    color: windowtext;

    border: 0.5pt solid windowtext;
  }
  .xl111 {
    text-align: left;

    background: #ffffff;
  }

  /* 行高样式 */
  tr {
    mso-height-source: auto;
    mso-ruby-visibility: none;
  }

  col {
    mso-width-source: auto;
    mso-ruby-visibility: none;
  }
</style>
