<template>
  <div class="special-order-list-container">
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <div class="filter-left">
        <!-- QC订单ID搜索框 -->
        <el-input
          v-model.trim="state.filterForm.qcOrderId"
          placeholder="请输入QC订单ID"
          clearable
          @input="queryData()"
          style="width: 150px"
        />

        <!-- 搜索框 -->
        <el-input
          v-model.trim="state.filterForm.keywords"
          placeholder="请输入产品名/特采单号/订单号/料号/入库号进行搜索"
          clearable
          class="search-input"
          @input="queryData()"
          style="width: 220px"
        />

        <!-- 状态筛选 -->
        <el-select
          v-model="state.filterForm.status"
          placeholder="选择状态"
          clearable
          class="status-select"
          @change="queryData()"
          style="width: 180px"
        >
          <el-option
            v-for="item in state.statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <!-- 筛选时间 -->
        <el-date-picker
          v-model="state.filterForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          class="date-picker"
          @change="queryData()"
        />

        <!-- 重置按钮 -->
        <el-button @click="handleRefresh">重置</el-button>

        <!-- 我审核的开关 -->
        <div class="switch-wrapper">
          <span class="switch-label">我审核的</span>
          <el-switch
            v-model="state.filterForm.myApproval"
            @change="queryData()"
          />
        </div>
      </div>

      <div class="filter-right">
        <!-- 导出报告 -->
        <!-- <el-button type="success" @click="handleExport">导出报告</el-button> -->
      </div>
    </div>

    <el-table
      ref="tableRef"
      v-loading="state.listLoading"
      :data="state.list"
      @selection-change="setSelectRows"
      border
      style="width: 100%"
      height="calc(100vh - 280px)"
      @sort-change="handleSort"
      @row-dblclick="handleViewOrApprove"
      size="small"
      :row-style="{ height: '40px' }"
    >
      <el-table-column type="selection" width="50" />

      <el-table-column prop="order_no" width="160" label="特采单号" />

      <!-- 产品信息列 -->
      <el-table-column label="产品名称" width="300" show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            <div>{{ row.qc_order?.prod_name || '-' }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 规格描述 -->
      <el-table-column label="规格" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.qc_order?.prod_spec || '-' }}
        </template>
      </el-table-column>

      <el-table-column label="料号" width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            <div>{{ row.qc_order?.prod_code || '-' }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="订单号" width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            <div>{{ row.qc_order?.order_no || '-' }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="入库单号" width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            <div>{{ row.qc_order?.enter_no || '-' }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 数量 -->
      <el-table-column label="数量" width="70">
        <template #default="{ row }">
          {{ row.qc_order?.defective_num || 0 }}/{{
            row.qc_order?.examine_num || 0
          }}
        </template>
      </el-table-column>

      <!-- 审批状态 -->
      <el-table-column label="审批状态" width="80">
        <template #default="{ row }">
          <span
            :class="[
              'status-badge',
              `status-badge--${getStatusTagType(row.status)}`,
            ]"
          >
            {{ getStatusText(row.status) }}
          </span>
        </template>
      </el-table-column>

      <!-- 审批人信息 -->
      <el-table-column label="审批人" width="120">
        <template #default="{ row }">
          <div class="approver-info">
            <el-avatar
              :size="24"
              :src="getApproverInfo(row).avatar"
              :class="getApproverAvatarClass(row)"
            >
              <span>{{ getApproverInfo(row).name?.charAt(0) || '未' }}</span>
            </el-avatar>
            <div class="approver-name">
              {{ getApproverInfo(row).name }}
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 申请时间 -->
      <el-table-column
        prop="created_at"
        label="申请时间"
        sortable="custom"
        width="160"
      />

      <!-- 操作 -->
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              link
            >
              编辑
            </el-button>
            <el-button
              v-if="row.status === 'approved'"
              type="primary"
              size="small"
              @click="handleReport(row)"
              link
            >
              报告
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      background
      :current-page="state.queryForm.page"
      :page-size="state.queryForm.limit"
      :page-sizes="[10, 20, 50, 100]"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 审批详情弹窗 -->
    <ApprovalDetail
      v-if="state.showApprovalDialog"
      :visible="state.showApprovalDialog"
      :order-id="state.currentOrderId"
      :is-my-approval="state.isMyApproval"
      @close="handleCloseApproval"
      @refresh="queryData"
    />

    <!-- 报告弹窗 -->
    <Report
      ref="reportRef"
      v-if="state.showReportDialog"
      :visible="state.showReportDialog"
      :data="state.currentReportData"
      @close="handleCloseReport"
    />
  </div>
</template>

<script setup>
  import { reactive, ref, onMounted, onUnmounted, inject } from 'vue'
  import { useRoute } from 'vue-router'
  // import { Search } from '@element-plus/icons-vue' // 已移除
  import ApprovalDetail from './ApprovalDetail.vue'
  import Report from './Report.vue'
  import { getStatusTagType, getStatusText } from '../utils/constants'
  import { getList } from '@/api/oaQcSpecialOrder'

  const $baseMessage = inject('$baseMessage')
  const route = useRoute()

  const tableRef = ref()
  const reportRef = ref()

  const state = reactive({
    listLoading: false,
    list: [],
    total: 0,
    selectRows: [],
    queryForm: {
      page: 1,
      limit: 20,
      filter: {},
      op: {},
      sort: 'created_at',
      order: 'desc',
    },
    // 筛选表单数据
    filterForm: {
      keywords: '',
      qcOrderId: '',
      status: '',
      myApproval: false,
      dateRange: [],
    },
    statusList: [
      { label: '全部状态', value: '' },
      { label: '待审批', value: 'pending' },
      { label: '审批中', value: 'step1' },
      { label: '已通过', value: 'approved' },
      { label: '已驳回', value: 'rejected' },
      { label: '已取消', value: 'cancelled' },
    ],
    isEditMode: false,
    currentOrderId: null,
    showApprovalDialog: false,
    showReportDialog: false,
    isMyApproval: false,
    currentReportData: null,
    queryTimer: null, // 防抖定时器
  })

  const queryData = () => {
    if (state.queryTimer) {
      clearTimeout(state.queryTimer)
    }

    state.queryTimer = setTimeout(() => {
      state.listLoading = true

      buildQueryParams()

      getList(state.queryForm)
        .then((res) => {
          state.list = res.data.data || []
          state.total = res.data.total || 0
          state.listLoading = false
        })
        .catch((error) => {
          console.error('获取列表失败:', error)
          $baseMessage('获取列表失败', 'error')
          state.list = []
          state.total = 0
          state.listLoading = false
        })
    }, 880) // 880毫秒防抖
  }

  // 立即执行查询（用于分页、排序等不需要防抖的操作）
  const queryDataImmediate = () => {
    if (state.queryTimer) {
      clearTimeout(state.queryTimer)
      state.queryTimer = null
    }

    state.listLoading = true

    buildQueryParams()

    getList(state.queryForm)
      .then((res) => {
        state.list = res.data.data || []
        state.total = res.data.total || 0
        state.listLoading = false
      })
      .catch((error) => {
        console.error('获取列表失败:', error)
        $baseMessage('获取列表失败', 'error')
        state.list = []
        state.total = 0
        state.listLoading = false
      })
  }

  // 构建查询参数
  const buildQueryParams = () => {
    // 重置filter和op
    state.queryForm.filter = {}
    state.queryForm.op = {}
    state.queryForm.search = {}

    // 状态筛选
    if (state.filterForm.status) {
      state.queryForm.filter.status = state.filterForm.status
      state.queryForm.op.status = '='
    }

    // 我的待审批
    if (state.filterForm.myApproval) {
      state.queryForm.filter.myApproval = true
    }

    // 日期范围
    if (state.filterForm.dateRange && state.filterForm.dateRange.length === 2) {
      state.queryForm.filter.start_date = state.filterForm.dateRange[0]
      state.queryForm.filter.end_date = state.filterForm.dateRange[1]
    }

    // 关键词搜索
    if (state.filterForm.keywords) {
      state.queryForm.filter.keywords = state.filterForm.keywords
    }

    // QC订单ID搜索
    if (state.filterForm.qcOrderId) {
      state.queryForm.filter.qc_order_id = state.filterForm.qcOrderId
      state.queryForm.op.qc_order_id = '='
    }
  }

  const handleRefresh = () => {
    // 重置筛选表单
    state.filterForm = {
      keywords: '',
      qcOrderId: '',
      status: '',
      myApproval: false,
      dateRange: [],
    }

    // 重置查询参数
    state.queryForm = {
      page: 1,
      limit: 20,
      filter: {},
      op: {},
      sort: 'created_at',
      order: 'desc',
    }

    queryDataImmediate() // 刷新使用立即执行
  }

  const setSelectRows = (rows) => {
    state.selectRows = rows
  }

  const handleSort = ({ prop, order }) => {
    state.queryForm.sort = prop
    state.queryForm.order = order === 'ascending' ? 'asc' : 'desc'
    queryDataImmediate() // 排序使用立即执行
  }

  const handleSizeChange = (size) => {
    state.queryForm.limit = size
    queryDataImmediate() // 分页使用立即执行
  }

  const handleCurrentChange = (page) => {
    state.queryForm.page = page
    queryDataImmediate() // 分页使用立即执行
  }

  const handleEdit = (row) => {
    // 进入审批弹窗
    state.showApprovalDialog = true
    state.currentOrderId = row.id
    state.isMyApproval = isMyApprovalStep(row)
  }

  // 判断是否是我的审批步骤
  const isMyApprovalStep = (row) => {
    return ['pending', 'step1', 'step2', 'step3', 'step4'].includes(row.status)
  }

  const handleViewOrApprove = (row) => {
    state.currentOrderId = row.id
    state.isMyApproval = isMyApprovalStep(row)
    state.showApprovalDialog = true
  }

  const handleCloseApproval = () => {
    state.showApprovalDialog = false
    state.currentOrderId = null
    state.isMyApproval = false
  }

  // 复制功能（已移除，保留注释）
  // const handleCopy = (row) => {
  //   $baseMessage('复制功能开发中...', 'info')
  // }

  // 导出功能
  // const handleExport = () => {
  //   $baseMessage('导出功能开发中...', 'info')
  // }

  // 报告功能
  const handleReport = (row) => {
    // 显示报告弹窗
    state.currentReportData = row
    state.showReportDialog = true
    // 调用Report组件的方法显示报告
    setTimeout(() => {
      reportRef.value.showReport(row)
    }, 100)
  }

  const handleCloseReport = () => {
    state.showReportDialog = false
    state.currentReportData = null
  }

  // 获取审批人信息（姓名和头像）
  const getApproverInfo = (row) => {
    // 如果有当前审批人（审批中状态）
    if (row.current_approver_name) {
      return {
        name: row.current_approver_name,
        avatar:
          row.current_approver?.avatar ||
          row.current_approver?.thumb_avatar ||
          null,
      }
    }

    // 对于已完成的订单，获取最后操作人信息
    const completedStatuses = ['approved', 'rejected', 'cancelled', 'returned']
    if (completedStatuses.includes(row.status)) {
      // 从审批历史中获取最后一条记录的操作人
      if (row.approvals && row.approvals.length > 0) {
        // 按时间排序，获取最新的审批记录
        const sortedApprovals = [...row.approvals].sort(
          (a, b) => new Date(b.created_at) - new Date(a.created_at)
        )
        const lastApproval = sortedApprovals[0]

        if (lastApproval && lastApproval.approver_name) {
          return {
            name: lastApproval.approver_name,
            avatar:
              lastApproval.approver?.avatar ||
              lastApproval.approver?.thumb_avatar ||
              null,
          }
        }
      }

      // 如果没有审批记录，使用创建人（适用于取消操作）
      if (row.creator_name) {
        return {
          name: row.creator_name,
          avatar: null, // 创建人头像暂时不获取
        }
      }
    }

    return {
      name: '未分配',
      avatar: null,
    }
  }

  // 获取审批人头像样式
  const getApproverAvatarClass = (row) => {
    // 如果有当前审批人，显示待审批样式
    if (row.current_approver_name) {
      return 'avatar-pending'
    }

    // 根据最终状态显示不同颜色
    switch (row.status) {
      case 'approved':
        return 'avatar-approved'
      case 'rejected':
        return 'avatar-rejected'
      case 'cancelled':
        return 'avatar-cancelled'
      case 'returned':
        return 'avatar-returned'
      default:
        return 'avatar-unassigned'
    }
  }

  // 使用导入的工具函数，不需要重复定义

  onMounted(() => {
    queryDataImmediate() // 初始加载使用立即执行

    // 处理通知点击跳转
    handleNotificationClick()
  })

  onUnmounted(() => {
    // 清理防抖定时器
    if (state.queryTimer) {
      clearTimeout(state.queryTimer)
      state.queryTimer = null
    }
  })

  // 处理通知点击跳转
  const handleNotificationClick = async () => {
    const orderNo = route.query.orderNo
    const autoApproval = route.query.autoApproval
    const qcOrderId = route.query.qcOrderId

    if (orderNo && autoApproval === '1') {
      try {
        // 等待数据加载完成
        await new Promise((resolve) => {
          const checkData = () => {
            if (!state.listLoading && state.list.length > 0) {
              resolve()
            } else {
              setTimeout(checkData, 100)
            }
          }
          checkData()
        })

        // 根据orderNo查找并定位到对应订单
        await searchAndLocateOrder(orderNo)
      } catch (error) {
        console.error('处理通知点击失败:', error)
        $baseMessage('订单加载失败，请手动查找', 'error')
      }
    } else if (qcOrderId) {
      try {
        // 等待数据加载完成
        await new Promise((resolve) => {
          const checkData = () => {
            if (!state.listLoading) {
              resolve()
            } else {
              setTimeout(checkData, 100)
            }
          }
          checkData()
        })

        // 根据qcOrderId查找并定位到对应订单
        await searchAndLocateByQcOrderId(qcOrderId)
      } catch (error) {
        console.error('处理QC订单ID跳转失败:', error)
        $baseMessage('订单加载失败，请手动查找', 'error')
      }
    }
  }

  // 搜索并定位订单
  const searchAndLocateOrder = async (orderNo) => {
    // 设置搜索条件
    state.filterForm.keywords = orderNo

    // 执行搜索
    queryData()

    // 等待搜索完成
    await new Promise((resolve) => {
      const checkSearch = () => {
        if (!state.listLoading) {
          resolve()
        } else {
          setTimeout(checkSearch, 100)
        }
      }
      checkSearch()
    })

    // 查找目标订单
    const targetOrder = state.list.find((item) => item.order_no === orderNo)

    if (targetOrder) {
      // 高亮显示目标行
      highlightRow(targetOrder)

      // 延迟一下确保DOM更新完成，然后自动打开审批弹窗
      setTimeout(() => {
        openApprovalDialog(targetOrder)
      }, 500)
    } else {
      $baseMessage('未找到对应的订单', 'warning')
    }
  }

  // 根据QC订单ID搜索并定位订单
  const searchAndLocateByQcOrderId = async (qcOrderId) => {
    // 设置QC订单ID搜索条件
    state.filterForm.qcOrderId = qcOrderId

    // 执行搜索（立即执行，不需要防抖）
    queryDataImmediate()

    // 等待搜索完成
    await new Promise((resolve) => {
      const checkSearch = () => {
        if (!state.listLoading) {
          resolve()
        } else {
          setTimeout(checkSearch, 100)
        }
      }
      checkSearch()
    })

    // 查找目标订单（通过qc_order_id字段匹配）
    const targetOrder = state.list.find((item) => item.qc_order_id == qcOrderId)

    if (targetOrder) {
      // 高亮显示目标行
      highlightRow(targetOrder)

      // 显示成功消息
      $baseMessage(`已定位到QC订单ID为 ${qcOrderId} 的特采订单`, 'success')

      // 清理URL参数
      cleanUrlParams()
    } else {
      $baseMessage(`未找到QC订单ID为 ${qcOrderId} 的特采订单`, 'warning')
      setTimeout(() => {
        cleanUrlParams(['qcOrderId'])
      }, 1000)
    }
  }

  // 高亮显示行
  const highlightRow = (rowData) => {
    if (tableRef.value) {
      tableRef.value.setCurrentRow(rowData)
    }
  }

  // 自动打开审批弹窗
  const openApprovalDialog = (orderData) => {
    state.currentOrderId = orderData.id
    state.isMyApproval = isMyApprovalStep(orderData)
    state.showApprovalDialog = true

    // 清理URL参数，避免刷新页面重复触发
    cleanUrlParams()
  }

  // 清理URL参数和搜索关键词
  // @param {Array} fieldsToClean - 需要清理的filterForm字段数组，默认为['keywords']
  // 使用示例：
  // cleanUrlParams() - 清理keywords字段（默认）
  // cleanUrlParams(['qcOrderId']) - 清理qcOrderId字段
  // cleanUrlParams(['keywords', 'qcOrderId']) - 清理多个字段
  const cleanUrlParams = (fieldsToClean = ['keywords']) => {
    const newUrl = window.location.pathname + window.location.hash.split('?')[0]
    window.history.replaceState({}, '', newUrl)

    // 延迟清理指定字段，给用户一些时间看到搜索结果
    setTimeout(() => {
      // 清理指定的filterForm字段
      fieldsToClean.forEach((field) => {
        if (field in state.filterForm) {
          state.filterForm[field] = ''
        }
      })

      // 重新加载数据，显示完整列表
      queryDataImmediate()
    }, 2000) // 2秒后清理
  }
</script>

<style lang="scss" scoped>
  .special-order-list-container {
    padding: 20px;
  }

  // 筛选栏样式
  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 16px;

    .filter-left {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .search-input {
        width: 140px;
        flex-shrink: 0;

        :deep(.el-input__wrapper) {
          box-shadow: 0 0 0 1px #dcdfe6 inset;

          &:hover {
            box-shadow: 0 0 0 1px #c0c4cc inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px #409eff inset;
          }
        }
      }

      .qc-search-input {
        width: 150px;
        flex-shrink: 0;

        :deep(.el-input__wrapper) {
          box-shadow: 0 0 0 1px #e6a23c inset;
          background-color: #fdf6ec;

          &:hover {
            box-shadow: 0 0 0 1px #e6a23c inset;
            background-color: #faecd8;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px #e6a23c inset;
            background-color: #ffffff;
          }
        }

        :deep(.el-input__inner) {
          color: #e6a23c;
          font-weight: 500;

          &::placeholder {
            color: #e6a23c;
            opacity: 0.7;
          }
        }
      }

      .status-select {
        width: 120px;
        flex-shrink: 0;

        :deep(.el-select__wrapper) {
          box-shadow: 0 0 0 1px #dcdfe6 inset;

          &:hover {
            box-shadow: 0 0 0 1px #c0c4cc inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px #409eff inset;
          }
        }
      }

      .date-picker {
        width: 120px;
        flex-shrink: 0;

        :deep(.el-date-editor) {
          box-shadow: 0 0 0 1px #dcdfe6 inset;

          &:hover {
            box-shadow: 0 0 0 1px #c0c4cc inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px #409eff inset;
          }
        }
      }

      .switch-wrapper {
        display: flex;
        align-items: center;
        gap: 6px;
        flex-shrink: 0;

        .switch-label {
          font-size: 13px;
          color: #606266;
          white-space: nowrap;
        }
      }

      .el-button {
        height: 32px;
        padding: 6px 12px;
        font-size: 13px;
        flex-shrink: 0;
      }
    }

    .filter-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: auto;

      .el-button {
        height: 32px;
        padding: 8px 16px;
        font-size: 14px;
      }
    }

    // 响应式设计
    @media (max-width: 1200px) {
      .filter-left {
        width: 60%;
        gap: 10px;

        .search-input {
          width: 120px;
        }

        .status-select {
          width: 90px;
        }

        .date-picker {
          width: 140px;
        }
      }
    }

    @media (max-width: 1024px) {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .filter-left {
        width: 100%;
        justify-content: flex-start;
        gap: 10px;

        .search-input {
          width: 140px;
        }

        .status-select {
          width: 100px;
        }

        .date-picker {
          width: 160px;
        }
      }

      .filter-right {
        width: 100%;
        justify-content: flex-end;
      }
    }

    @media (max-width: 768px) {
      .filter-left {
        gap: 8px;

        .search-input,
        .status-select,
        .date-picker {
          width: auto;
          min-width: 100px;
          flex: 1;
        }

        .switch-wrapper {
          .switch-label {
            font-size: 12px;
          }
        }
      }
    }
  }

  // 审批人信息样式
  .approver-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;

    .approver-name {
      font-size: 14px;
      color: #333333;
      line-height: 1;
    }

    // 头像状态样式
    .avatar-pending {
      background-color: #409eff;
    }

    .avatar-approved {
      background-color: #67c23a;
    }

    .avatar-rejected {
      background-color: #f56c6c;
    }

    .avatar-cancelled {
      background-color: #909399;
    }

    .avatar-returned {
      background-color: #e6a23c;
    }

    .avatar-unassigned {
      background-color: #dcdfe6;
      color: #909399;
    }
  }

  // 操作按钮样式
  .action-buttons {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    .el-button {
      font-size: 14px;
      height: auto;
      min-height: 24px;

      &.is-link {
        color: #3977f3;

        &:hover {
          color: #3977f3;
        }
      }
    }
  }

  // 表格全局样式
  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #fafafa;
          color: #606266;
          font-weight: 500;
          font-size: 12px;
          padding: 8px 0;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        td {
          font-size: 12px;
          border-bottom: 1px solid #ebeef5;
        }
      }
    }

    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }

    // 自定义状态标签样式 - 按设计稿规范
    .status-badge {
      display: inline-block;
      height: 24px;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 600;
      line-height: 14px;
      text-align: center;
      white-space: nowrap;
      border: none;
      cursor: default;
      box-sizing: border-box;

      // 申请中 - primary
      &--primary {
        background-color: #ecf2ff;
        color: #3977f3;
      }

      // 已通过 - success
      &--success {
        background-color: #e5fcf4;
        color: #18be6f;
      }

      // 待审批 - warning (映射为申请中样式)
      &--warning {
        background-color: #ecf2ff;
        color: #3977f3;
      }

      // 已驳回 - danger
      &--danger {
        background-color: #ffebe9;
        color: #ff5e4b;
      }

      // 已取消 - info
      &--info {
        background-color: #f5f5f5;
        color: #999999;
      }
    }
  }

  // 头像样式
  :deep(.el-avatar) {
    font-size: 11px;
  }
</style>
