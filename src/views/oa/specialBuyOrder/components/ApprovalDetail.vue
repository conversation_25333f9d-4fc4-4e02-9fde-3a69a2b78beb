<template>
  <fireflyDialog
    v-model="dialogVisible"
    :show-default-button="false"
    width="60%"
    :max-height="'calc(100vh - 200px)'"
  >
    <template #header>
      <div class="product-header">
        <div class="product-title">
          <div class="title-left">
            <h2>{{ state.orderInfo.qc_order?.prod_name || '未知产品' }}</h2>
            <span
              :class="[
                'status-badge',
                `status-badge--${getStatusTagType(state.orderInfo.status)}`,
              ]"
            >
              {{ getStatusText(state.orderInfo.status) }}
            </span>
          </div>
          <div class="title-right">
            <!-- 审批按钮 - 仅当用户有审批权限时显示 -->
            <button
              v-if="canCurrentUserApprove"
              class="action-btn action-btn--primary"
              @click="showApprovalDialog"
            >
              <vab-icon is-custom-svg icon="edit" class="btn-icon" />
              审批
            </button>

            <!-- 申请人操作按钮 - 仅当订单被退回且是申请人时显示 -->
            <button
              v-if="canCurrentUserResubmit"
              class="action-btn action-btn--warning"
              @click="showResubmitDialog"
            >
              <vab-icon is-custom-svg icon="refresh" class="btn-icon" />
              重新提交
            </button>

            <button
              v-if="canCurrentUserResubmit"
              class="action-btn action-btn--danger"
              @click="cancelOrder"
            >
              <vab-icon is-custom-svg icon="close" class="btn-icon" />
              取消申请
            </button>

            <!-- 审批历史按钮 -->
            <button
              class="action-btn action-btn--secondary"
              @click="showHistoryDialog"
            >
              <el-icon>
                <Clock />
              </el-icon>
              审批历史
            </button>

            <!-- 编辑按钮 - 始终显示 -->
            <!-- <button
              class="action-btn action-btn--secondary"
              @click="handleEdit"
            >
              <vab-icon is-custom-svg icon="edit" class="btn-icon" />
              编辑
            </button> -->
          </div>
        </div>
      </div>
    </template>

    <div class="approval-container">
      <!-- 产品信息头部 -->
      <div class="product-info-grid">
        <div class="info-row">
          <div class="info-item">
            <span class="label">不合格数量</span>
            <span class="value">
              {{ state.orderInfo.qc_order?.defective_num || 0 }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">总数量</span>
            <span class="value">
              {{ state.orderInfo.qc_order?.examine_num || 0 }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">不合格率</span>
            <span class="value">
              {{ state.orderInfo.defective_rate || 0 }}%
            </span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">QC订单号</span>
            <span class="value">
              {{ state.orderInfo.qc_order?.order_no || '-' }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">料号</span>
            <span class="value">
              {{ state.orderInfo.qc_order?.prod_code || '-' }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">特采订单号</span>
            <span class="value">{{ state.orderInfo.order_no }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">供应商</span>
            <span class="value">
              {{
                state.supplierInfo?.NAME ||
                state.orderInfo.qc_order?.factory_name ||
                '-'
              }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">申请人</span>
            <span class="value">{{ state.orderInfo.creator_name }}</span>
          </div>
          <div class="info-item">
            <span class="label">申请时间</span>
            <span class="value">{{ state.orderInfo.created_at }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">产品规格</span>
            <span class="value">
              {{ state.orderInfo.qc_order?.prod_spec || '-' }}
            </span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">申请原因</span>
            <span class="value">{{ state.orderInfo.special_reason }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">特采范围</span>
            <span class="value">
              {{ state.orderInfo.special_scope || '-' }}
            </span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">特采详情</span>
            <div class="special-details-display">
              <span class="value">
                {{ state.orderInfo.special_details || '-' }}
              </span>
              <el-button
                v-if="canCurrentUserEditSpecialDetails"
                type="primary"
                size="small"
                @click="showSpecialDetailsDialog"
                style="margin-left: 12px"
              >
                编辑详情
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="divider-container">
        <hr class="divider" />
      </div>

      <!-- 审批进度时间线 -->
      <div class="approval-timeline">
        <h3>审批进度</h3>

        <div class="timeline-container">
          <div
            v-for="(step, index) in state.approvalSteps"
            :key="index"
            class="timeline-step"
            :class="{
              completed: step.status === 'completed',
              current: step.status === 'current',
              rejected: step.status === 'rejected',
              pending: step.status === 'pending',
            }"
          >
            <!-- 时间线图标 -->
            <div class="step-icon">
              <el-icon v-if="step.status === 'completed'" class="success-icon">
                <Check />
              </el-icon>
              <el-icon
                v-else-if="step.status === 'rejected'"
                class="error-icon"
              >
                <Close />
              </el-icon>
              <div
                v-else-if="step.status === 'current'"
                class="current-dot"
              ></div>
              <div v-else class="pending-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
              </div>
            </div>

            <!-- 步骤内容 -->
            <div class="step-content">
              <div class="step-header">
                <span class="step-title">{{ step.title }}</span>
              </div>
              <div class="step-details">
                <div v-if="step.approver" class="step-approver">
                  审批人：{{ step.approver }}
                  <span v-if="step.position" class="step-position">
                    ({{ step.position }})
                  </span>
                  <span v-else-if="step.department" class="step-department">
                    ({{ step.department }})
                  </span>
                </div>
                <div v-if="step.time" class="step-time">
                  {{ step.time }}
                </div>
                <div
                  v-if="step.remark"
                  class="step-remark"
                  :class="{
                    'remark-approved': step.status === 'approved',
                    'remark-completed': step.status === 'completed',
                    'remark-rejected': step.status === 'rejected',
                  }"
                >
                  {{ step.remark_label }}{{ step.remark }}
                </div>
              </div>
            </div>

            <!-- 连接线 -->
            <div
              v-if="index < state.approvalSteps.length - 1"
              class="step-connector"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </fireflyDialog>

  <!-- 审批弹窗 -->
  <fireflyDialog
    v-model="state.showApprovalDialog"
    title="审批"
    width="500px"
    :before-close="handleCloseApprovalDialog"
  >
    <el-form
      ref="approvalFormRef"
      :model="state.approvalForm"
      :rules="state.approvalRules"
      label-width="0"
    >
      <div class="approval-status-section">
        <div class="approval-status-label">
          <span style="color: #ff5e4b">*</span>
          审批状态
        </div>
        <el-radio-group
          v-model="state.approvalForm.action"
          class="approval-radio-group"
        >
          <el-radio label="approve" size="large">同意</el-radio>
          <el-radio label="reject" size="large">驳回</el-radio>
        </el-radio-group>
      </div>

      <div class="approval-remark-section">
        <div class="approval-remark-label">审批意见</div>
        <el-form-item prop="remark">
          <el-input
            v-model="state.approvalForm.remark"
            type="textarea"
            :rows="6"
            :placeholder="
              state.approvalForm.action === 'approve'
                ? '请输入审批意见（选填）'
                : '请输入驳回理由'
            "
            class="approval-textarea"
          />
        </el-form-item>
      </div>

      <!-- 驳回层级选择 -->
      <div
        v-if="state.approvalForm.action === 'reject'"
        class="reject-level-section"
      >
        <div class="approval-remark-label">驳回到</div>
        <el-form-item prop="reject_to_step">
          <el-select
            v-model="state.approvalForm.reject_to_step"
            placeholder="请选择驳回层级"
            style="width: 100%"
          >
            <el-option
              v-for="level in availableRejectLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="approval-dialog-footer">
        <el-button @click="handleCloseApprovalDialog">取消</el-button>
        <el-button
          type="primary"
          :loading="state.approving"
          @click="handleSubmitApproval"
        >
          确定
        </el-button>
      </div>
    </template>
  </fireflyDialog>

  <!-- 重新提交对话框 -->
  <fireflyDialog
    v-model="state.showResubmitDialog"
    title="重新提交申请"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="resubmitFormRef"
      :model="state.resubmitForm"
      label-width="120px"
      class="resubmit-form"
    >
      <el-form-item
        label="申请原因"
        prop="special_reason"
        :rules="[
          { required: true, message: '请输入申请原因', trigger: 'blur' },
          { min: 10, message: '申请原因至少10个字符', trigger: 'blur' },
        ]"
      >
        <el-input
          v-model="state.resubmitForm.special_reason"
          type="textarea"
          :rows="4"
          placeholder="请详细说明特采申请的原因"
        />
      </el-form-item>

      <el-form-item
        label="特采范围"
        prop="special_scope"
        :rules="[
          { required: true, message: '请输入特采范围', trigger: 'blur' },
        ]"
      >
        <el-input
          v-model="state.resubmitForm.special_scope"
          type="textarea"
          :rows="3"
          placeholder="请说明特采的范围和适用条件"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCloseResubmitDialog">取消</el-button>
        <el-button type="primary" @click="handleResubmit">
          确定重新提交
        </el-button>
      </div>
    </template>
  </fireflyDialog>

  <!-- 特采详情编辑弹窗 -->
  <fireflyDialog
    v-model="state.showSpecialDetailsDialog"
    title="编辑特采详情"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="specialDetailsFormRef"
      :model="state.specialDetailsForm"
      :rules="state.specialDetailsRules"
      label-width="100px"
    >
      <el-form-item label="特采详情" prop="special_details">
        <el-input
          v-model="state.specialDetailsForm.special_details"
          type="textarea"
          :rows="6"
          placeholder="请输入特采详情"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCloseSpecialDetailsDialog">取消</el-button>
        <el-button
          type="primary"
          :loading="state.savingSpecialDetails"
          @click="handleSaveSpecialDetails"
        >
          保存
        </el-button>
      </div>
    </template>
  </fireflyDialog>

  <!-- 审批历史组件 -->
  <ApprovalHistory
    :visible="state.showHistoryDialog"
    :approval-history="state.allApprovalHistory"
    @close="handleCloseHistoryDialog"
  />
</template>

<script setup>
  import { useUserStore } from '@/store/modules/user'
  import { reactive, ref, computed, watch, inject, nextTick } from 'vue'
  import { Check, Close, Clock } from '@element-plus/icons-vue'
  import {
    REJECT_LEVELS,
    getCurrentStepByStatus,
    getStatusTagType,
    getStatusText,
  } from '../utils/constants'

  import {
    getDetail,
    approve,
    resubmit,
    cancel,
    updateOrderInfo,
  } from '@/api/oaQcSpecialOrder'
  import { getErpSupplierInfo } from '@/api/oaQcerp'
  import ApprovalHistory from './ApprovalHistory.vue'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: Number,
      required: true,
    },
    isMyApproval: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['close', 'refresh'])

  const userStore = useUserStore()

  const { user_id } = userStore

  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  const approvalFormRef = ref()
  const resubmitFormRef = ref()
  const specialDetailsFormRef = ref()

  // 品质经理用户ID常量（与后端保持一致）
  const QUALITY_MANAGER_ID = 294 // 胡宏志

  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => {
      if (!val) {
        emit('close')
      }
    },
  })

  const state = reactive({
    loading: false,
    approving: false,
    showRejectLevel: false,
    showApprovalDialog: false,
    showResubmitDialog: false,
    showHistoryDialog: false,
    showSpecialDetailsDialog: false, // 特采详情编辑弹窗
    orderInfo: {},
    approvalSteps: [],
    allApprovalHistory: [], // 完整的审批历史记录
    supplierInfo: null, // 供应商信息
    approvalForm: {
      action: 'approve', // 添加审批动作
      remark: '',
      reject_to_step: 0,
    },
    resubmitForm: {
      special_reason: '',
      special_scope: '',
    },
    // 特采详情编辑相关
    specialDetailsForm: {
      special_details: '',
    },
    savingSpecialDetails: false,
    approvalRules: {
      remark: [
        {
          required: false,
          message: '请输入审批意见',
          trigger: 'blur',
        },
      ],
      reject_to_step: [
        {
          required: true,
          message: '请选择驳回层级',
          trigger: 'change',
          validator: (_, value, callback) => {
            if (
              state.approvalForm.action === 'reject' &&
              (value === null || value === undefined)
            ) {
              callback(new Error('请选择驳回层级'))
            } else {
              callback()
            }
          },
        },
      ],
    },
    specialDetailsRules: {
      special_details: [
        {
          required: true,
          message: '请输入特采详情',
          trigger: 'blur',
        },
      ],
    },
    user_id: user_id,
  })

  // 判断当前用户是否可以审批
  const canCurrentUserApprove = computed(() => {
    if (!state.user_id || !state.orderInfo) return false

    // 检查订单状态 - 已取消、已退回、已通过、已驳回的订单不能审批
    const nonApprovableStatuses = [
      'cancelled',
      'returned',
      'approved',
      'rejected',
    ]
    if (nonApprovableStatuses.includes(state.orderInfo.status)) {
      return false
    }

    // 申请人不能审批自己的申请
    // if (state.orderInfo.creator_id === state.user_id) {
    //   return false
    // }

    // 直接使用后端返回的current_approver_id判断
    return state.orderInfo.current_approver_id === state.user_id
  })

  // 判断当前用户是否可以重新提交
  const canCurrentUserResubmit = computed(() => {
    if (!state.user_id || !state.orderInfo) return false

    // 检查订单是否被退回且当前用户是申请人
    return (
      state.orderInfo.status === 'returned' &&
      state.orderInfo.creator_id === state.user_id
    )
  })

  // 判断当前用户是否可以编辑特采详情
  const canCurrentUserEditSpecialDetails = computed(() => {
    if (!state.user_id || !state.orderInfo) return false

    // 只有品质经理可以编辑（不限制审批步骤）
    return state.user_id === QUALITY_MANAGER_ID
  })

  // 可用的驳回层级选项
  const availableRejectLevels = computed(() => {
    const currentStep = getCurrentStepByStatus(state.orderInfo.status)
    // 只能驳回到当前步骤之前的层级
    return REJECT_LEVELS.filter((level) => level.step < currentStep)
  })

  // 获取默认驳回层级（上一级）
  const getDefaultRejectLevel = () => {
    const currentStep = getCurrentStepByStatus(state.orderInfo.status)
    const availableLevels = availableRejectLevels.value

    if (availableLevels.length === 0) {
      return null
    }

    // 找到上一级（当前步骤-1）
    const previousStep = currentStep - 1
    const previousLevel = availableLevels.find(
      (level) => level.step === previousStep
    )

    // 如果找到上一级，返回上一级；否则返回最后一个可用级别（最接近当前级别的）
    return previousLevel
      ? previousLevel.value
      : availableLevels[availableLevels.length - 1].value
  }

  // 获取预设审批人信息的函数
  const getPresetApproverInfo = (step) => {
    // 使用后端返回的真实审批人信息
    if (state.orderInfo.all_approvers && state.orderInfo.all_approvers[step]) {
      const approver = state.orderInfo.all_approvers[step]
      return {
        name: approver.name,
        department: approver.department,
        position: approver.position,
        avatar: approver.avatar,
      }
    }

    // 如果没有找到，返回默认信息
    const defaultApprovers = {
      1: { name: '采购审批人', department: '采购部' },
      2: { name: '品质经理', department: '品质部' },
      3: { name: 'PMC审批人', department: 'PMC部' },
      4: { name: '副总经理', department: '管理层' },
    }
    return defaultApprovers[step] || { name: '', department: '' }
  }

  // 获取最新一轮的审批记录
  const getLatestApprovalRound = (approvals) => {
    if (!approvals || approvals.length === 0) return []

    // 找到最大的轮次
    const maxRound = Math.max(
      ...approvals.map((approval) => approval.round || 1)
    )

    // 只返回最新轮次的审批记录
    return approvals.filter((approval) => (approval.round || 1) === maxRound)
  }

  // 获取步骤显示状态（考虑驳回重置逻辑）
  const getStepDisplayStatus = (stepNumber, orderInfo, approvals) => {
    // 如果订单已最终完成，显示实际状态
    if (['approved', 'rejected', 'cancelled'].includes(orderInfo.status)) {
      const stepApproval = approvals.find(
        (a) => a.step === stepNumber && a.action === 'approve'
      )
      return stepApproval ? 'approved' : 'pending'
    }

    // 获取当前轮次
    const currentRound = Math.max(...approvals.map((a) => a.round || 1))
    const currentRoundApprovals = approvals.filter(
      (a) => (a.round || 1) === currentRound
    )

    // 找到当前轮次最后一次驳回记录
    const lastReject = currentRoundApprovals
      .filter((a) => a.action === 'reject' && a.reject_to_step)
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0]

    if (lastReject && lastReject.reject_to_step) {
      console.log(
        `步骤${stepNumber}: 发现驳回记录，驳回到步骤${lastReject.reject_to_step}`
      )
      // 如果当前步骤 >= 驳回目标步骤，检查是否有驳回后的新审批
      if (stepNumber >= lastReject.reject_to_step) {
        const approvalAfterReject = currentRoundApprovals.find(
          (a) =>
            a.step === stepNumber &&
            a.action === 'approve' &&
            new Date(a.created_at) > new Date(lastReject.created_at)
        )
        const result = approvalAfterReject ? 'approved' : 'pending'
        console.log(`步骤${stepNumber}: 需要重置，结果=${result}`)
        return result
      }
    }

    // 其他情况，显示当前轮次该步骤的实际状态
    const stepApproval = currentRoundApprovals.find(
      (a) => a.step === stepNumber && a.action === 'approve'
    )
    return stepApproval ? 'approved' : 'pending'
  }

  // 转换审批历史数据格式
  const convertApprovalSteps = (approvals) => {
    // 预设的审批流程配置（固定的审批步骤）
    const steps = [
      {
        title: '采购审批',
        step: 1,
        status: 'pending',
        approver: '',
        department: '',
        position: '',
        time: '',
        remark: '',
        remark_label: '',
      },
      {
        title: '品质经理审批',
        step: 2,
        status: 'pending',
        approver: '',
        department: '',
        position: '',
        time: '',
        remark: '',
        remark_label: '',
      },
      {
        title: 'PMC审批',
        step: 3,
        status: 'pending',
        approver: '',
        department: '',
        position: '',
        time: '',
        remark: '',
        remark_label: '',
      },
      {
        title: '副总经理审批',
        step: 4,
        status: 'pending',
        approver: '',
        department: '',
        position: '',
        time: '',
        remark: '',
        remark_label: '',
      },
    ]

    // 为所有步骤设置预设审批人信息
    steps.forEach((step) => {
      const presetInfo = getPresetApproverInfo(step.step)
      step.approver = presetInfo.name
      step.department = presetInfo.department
      step.position = presetInfo.position
    })

    // 过滤出最新一轮的审批记录
    const latestApprovals = getLatestApprovalRound(approvals)

    // 使用新的状态判断逻辑更新步骤状态
    steps.forEach((step, index) => {
      const stepNumber = index + 1 // 步骤编号从1开始
      const displayStatus = getStepDisplayStatus(
        stepNumber,
        state.orderInfo,
        approvals
      )

      // 查找该步骤的审批记录（优先显示最新的）
      const stepApprovals = latestApprovals.filter((a) => a.step === stepNumber)
      const latestStepApproval = stepApprovals.sort(
        (a, b) => new Date(b.created_at) - new Date(a.created_at)
      )[0]

      if (latestStepApproval) {
        // 用真实的审批记录覆盖预设信息
        step.approver =
          latestStepApproval.approver?.name || latestStepApproval.approver_name
        step.department = latestStepApproval.approver?.department_text || ''
        step.position = latestStepApproval.approver?.position || ''
        step.time =
          latestStepApproval.approved_at || latestStepApproval.created_at
        step.remark = latestStepApproval.comment

        if (
          latestStepApproval.action === 'approve' &&
          displayStatus === 'approved'
        ) {
          step.status = 'completed'
          step.remark_label = '审批意见：'
        } else if (latestStepApproval.action === 'reject') {
          step.status = 'rejected'
          step.remark_label = '驳回原因：'
        } else if (latestStepApproval.action === 'returned') {
          step.status = 'rejected' // 退回也显示为拒绝状态（红色带X）
          step.remark_label = '退回原因：'
        } else {
          // 如果有审批记录但状态被重置，显示为待审批
          step.status = 'pending'
          step.remark_label = ''
          step.time = ''
          step.remark = ''
        }
      } else {
        // 没有审批记录，保持预设状态
        step.status = 'pending'
        step.remark_label = ''
      }
    })

    // 设置当前步骤（步骤索引需要减1）
    const currentStep = getCurrentStepByStatus(state.orderInfo.status)
    const currentStepIndex = currentStep - 1

    if (currentStepIndex >= 0 && currentStepIndex < steps.length) {
      steps[currentStepIndex].status = 'current'

      // 如果当前步骤还没有真实的审批记录，用当前审批人信息覆盖预设信息
      if (state.orderInfo.current_approver && !steps[currentStepIndex].time) {
        steps[currentStepIndex].approver = state.orderInfo.current_approver.name
        steps[currentStepIndex].department =
          state.orderInfo.current_approver.department_text || ''
        steps[currentStepIndex].position =
          state.orderInfo.current_approver.position || ''
        steps[currentStepIndex].remark_label = ''
      }
    }
    return steps
  }

  const loadOrderDetail = async () => {
    if (!props.orderId) {
      return
    }

    state.loading = true
    // 重置供应商信息，避免显示上一个订单的供应商信息
    state.supplierInfo = null

    try {
      // 真实API调用
      const response = await getDetail({ id: props.orderId })

      // 数据在response.data中
      state.orderInfo = response.data

      // 保存完整的审批历史记录
      state.allApprovalHistory = response.data.approvals || []

      // 转换审批历史数据格式（只显示最新轮次）
      state.approvalSteps = convertApprovalSteps(response.data.approvals || [])

      // 获取供应商信息
      await loadSupplierInfo()
    } catch (error) {
      $baseMessage('加载数据失败', 'error')
    } finally {
      state.loading = false
    }
  }

  // 获取供应商信息
  const loadSupplierInfo = async () => {
    try {
      const orderFactoryCode = state.orderInfo.qc_order?.order_factory_code
      if (orderFactoryCode) {
        const response = await getErpSupplierInfo({
          order_factory_code: orderFactoryCode,
        })
        state.supplierInfo = response.data
      }
    } catch (error) {
      console.error('获取供应商信息失败:', error)
    }
  }

  // 编辑特采申请单
  const handleEdit = () => {
    $baseMessage('编辑功能开发中...', 'info')
    // emit('edit', state.orderInfo)
  }

  // 显示特采详情编辑弹窗
  const showSpecialDetailsDialog = () => {
    if (!canCurrentUserEditSpecialDetails.value) return

    // 初始化表单数据
    state.specialDetailsForm.special_details =
      state.orderInfo.special_details || ''
    state.showSpecialDetailsDialog = true
  }

  // 关闭特采详情编辑弹窗
  const handleCloseSpecialDetailsDialog = () => {
    state.showSpecialDetailsDialog = false
    state.specialDetailsForm.special_details = ''
  }

  // 保存特采详情
  const handleSaveSpecialDetails = async () => {
    // 表单验证
    if (!specialDetailsFormRef.value) return

    try {
      await specialDetailsFormRef.value.validate()
    } catch (error) {
      return
    }

    // 如果值没有变化，不需要保存
    if (
      state.specialDetailsForm.special_details ===
      state.orderInfo.special_details
    ) {
      state.showSpecialDetailsDialog = false
      return
    }

    try {
      state.savingSpecialDetails = true

      const result = await updateOrderInfo({
        id: state.orderInfo.id,
        special_details: state.specialDetailsForm.special_details,
      })

      if (result) {
        $baseMessage('特采详情保存成功', 'success')

        // 更新本地数据
        state.orderInfo.special_details =
          state.specialDetailsForm.special_details

        // 关闭弹窗
        state.showSpecialDetailsDialog = false

        // 刷新数据
        emit('refresh')
      }
    } catch (error) {
      console.error('保存特采详情失败:', error)
      $baseMessage('保存特采详情失败', 'error')
    } finally {
      state.savingSpecialDetails = false
    }
  }

  // 显示重新提交对话框
  const showResubmitDialog = () => {
    // 初始化表单数据
    state.resubmitForm = {
      special_reason: state.orderInfo.special_reason || '',
      special_scope: state.orderInfo.special_scope || '',
    }
    state.showResubmitDialog = true
  }

  // 关闭重新提交对话框
  const handleCloseResubmitDialog = () => {
    state.showResubmitDialog = false
    state.resubmitForm = {
      special_reason: '',
      special_scope: '',
    }
  }

  // 处理重新提交
  const handleResubmit = async () => {
    try {
      // 表单验证
      const valid = await new Promise((resolve) => {
        resubmitFormRef.value.validate((valid) => {
          resolve(valid)
        })
      })

      if (!valid) {
        return
      }

      $baseConfirm('确定要重新提交这个特采申请单吗？', '确认操作', async () => {
        try {
          const data = {
            id: props.orderId,
            ...state.resubmitForm,
          }

          await resubmit(data)
          $baseMessage('重新提交成功', 'success')

          // 关闭对话框并刷新数据
          handleCloseResubmitDialog()

          // 重新获取订单详情以刷新审批进度
          await loadOrderDetail()

          emit('refresh')
        } catch (error) {
          console.error('重新提交失败:', error)
          $baseMessage('重新提交失败', 'error')
        }
      })
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  // 取消申请
  const cancelOrder = () => {
    $baseConfirm(
      '确定要取消这个特采申请单吗？取消后将无法恢复。',
      '确认取消',
      async () => {
        try {
          const data = {
            id: props.orderId,
            reason: '申请人主动取消',
          }

          await cancel(data)
          $baseMessage('取消成功', 'success')

          // 重新获取订单详情以刷新审批进度
          await loadOrderDetail()

          emit('refresh')
        } catch (error) {
          console.error('取消失败:', error)
          $baseMessage('取消失败', 'error')
        }
      }
    )
  }

  // 显示审批历史对话框
  const showHistoryDialog = () => {
    state.showHistoryDialog = true
  }

  // 关闭审批历史对话框
  const handleCloseHistoryDialog = () => {
    state.showHistoryDialog = false
  }

  const handleClose = () => {
    state.approvalForm = {
      action: 'approve',
      remark: '',
      reject_to_step: null,
    }
    state.showApprovalDialog = false
    emit('close')
  }

  // 使用导入的工具函数

  watch(
    () => props.visible,
    (newVal) => {
      if (newVal && props.orderId) {
        // 弹窗打开时立即加载数据
        loadOrderDetail()
      }
    },
    { immediate: true } // 改为立即执行
  )

  // 监听 orderId 变化
  watch(
    () => props.orderId,
    (newOrderId) => {
      if (newOrderId && props.visible) {
        loadOrderDetail()
      }
    }
  )

  // 移除 onMounted 钩子，避免重复调用
  // 数据加载由 watch(props.visible) 和 watch(props.orderId) 处理

  // 显示审批弹窗
  const showApprovalDialog = () => {
    state.showApprovalDialog = true
    // 重置表单，并设置驳回层级默认值为上一级
    const defaultRejectLevel = getDefaultRejectLevel()
    state.approvalForm = {
      action: 'approve',
      remark: '',
      reject_to_step: defaultRejectLevel,
    }
  }

  // 关闭审批弹窗
  const handleCloseApprovalDialog = () => {
    state.showApprovalDialog = false
    // 重置表单，并设置驳回层级默认值为上一级
    const defaultRejectLevel = getDefaultRejectLevel()
    state.approvalForm = {
      action: 'approve',
      remark: '',
      reject_to_step: defaultRejectLevel,
    }
  }

  // 提交审批
  const handleSubmitApproval = () => {
    if (!approvalFormRef.value) {
      console.error('approvalFormRef is not available')
      return
    }

    // 驳回时必须选择驳回层级
    if (
      state.approvalForm.action === 'reject' &&
      (state.approvalForm.reject_to_step === null ||
        state.approvalForm.reject_to_step === undefined)
    ) {
      $baseMessage('请选择驳回层级', 'warning')
      return
    }

    approvalFormRef.value.validate((valid) => {
      if (!valid) {
        console.log('表单验证失败')
        return
      }

      const actionText =
        state.approvalForm.action === 'approve' ? '同意' : '驳回'

      $baseConfirm(
        `确定要${actionText}这个特采申请单吗？`,
        '确认操作',
        async () => {
          state.approving = true

          try {
            // 真实API调用
            const data = {
              id: props.orderId,
              action: state.approvalForm.action,
              comment: state.approvalForm.remark,
            }

            // 只有在驳回时才传递 reject_to_step
            if (state.approvalForm.action === 'reject') {
              data.reject_to_step = state.approvalForm.reject_to_step
            }

            await approve(data)
            $baseMessage(`${actionText}成功`, 'success')
            state.showApprovalDialog = false

            // 重新获取订单详情以刷新审批进度
            await loadOrderDetail()

            emit('refresh')
          } catch (error) {
            console.error(`${actionText}失败:`, error)
            $baseMessage(`${actionText}失败`, 'error')
          } finally {
            state.approving = false
          }
        }
      )
    })
  }
</script>

<style lang="scss" scoped>
  .approval-container {
    padding: 0;
    min-height: 400px;
  }

  /* 产品信息头部 */
  .product-header {
    background: white;
    border-radius: 8px;
    padding: 0px 20px;
  }

  .product-title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .divider-container {
    padding: 0 20px;

    .divider {
      color: #dcdfe6;
      margin: 0;
    }
  }

  .status-badge {
    display: inline-block;
    height: 24px;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    line-height: 14px;
    text-align: center;
    white-space: nowrap;
    border: none;
    cursor: default;
    box-sizing: border-box;

    // 申请中 - primary
    &--primary {
      background-color: #ecf2ff;
      color: #3977f3;
    }

    // 已通过 - success
    &--success {
      background-color: #e5fcf4;
      color: #18be6f;
    }

    // 待审批 - warning (映射为申请中样式)
    &--warning {
      background-color: #ecf2ff;
      color: #3977f3;
    }

    // 已驳回 - danger
    &--danger {
      background-color: #ffebe9;
      color: #ff5e4b;
    }

    // 已取消 - info
    &--info {
      background-color: #f5f5f5;
      color: #999999;
    }
  }

  .product-info-grid {
    padding: 0px 20px 0px 20px;
  }

  .info-row {
    display: flex;
    margin-bottom: 8px;
  }

  .info-item {
    display: flex;
    flex: 1;
    align-items: center;

    &.full-width {
      flex: 100%;
    }

    .label {
      color: #909399;
      font-size: 14px;
      margin-right: 8px;
      min-width: 70px;
    }

    .value {
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .priority-high {
    color: #ff5e4b;
    font-weight: 600;
  }

  .step-department {
    color: #909399;
    font-size: 12px;
    margin-left: 4px;
  }

  /* 审批进度时间线 */
  .approval-timeline {
    background: white;
    border-radius: 8px;
    padding: 16px 20px 0px 20px;

    h3 {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .timeline-container {
    position: relative;
  }

  .timeline-step {
    display: flex;
    position: relative;
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    &.completed .step-icon {
      background-color: #18be6f;
      border: 2px solid #18be6f;
    }

    &.current .step-icon {
      background-color: #409eff;
      border: 2px solid #409eff;
    }

    &.rejected .step-icon {
      background-color: #ff5e4b;
      border: 2px solid #ff5e4b;
    }

    &.pending .step-icon {
      background-color: #dcdfe6;
      border: 2px solid #dcdfe6;
    }

    &.completed .step-connector {
      background-color: #18be6f;
    }
  }

  .step-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;
    position: relative;
    z-index: 2;
  }

  .success-icon,
  .error-icon {
    color: white;
    font-size: 12px;
  }

  .current-dot {
    width: 8px;
    height: 8px;
    background-color: white;
    border-radius: 50%;
  }

  .pending-dots {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
  }

  .pending-dots .dot {
    width: 3px;
    height: 3px;
    background-color: #fff;
    border-radius: 50%;
  }

  .step-content {
    flex: 1;
    padding-top: 2px;
  }

  .step-header {
    margin-bottom: 8px;
  }

  .step-title {
    font-weight: 600;
    font-size: 14px;
    color: #303133;
  }

  .step-details {
    font-size: 12px;
    line-height: 1.4;
  }

  .step-approver {
    color: #606266;
    margin-bottom: 2px;
  }

  .step-time {
    color: #909399;
    margin-bottom: 2px;
  }

  .step-remark {
    color: #606266;

    &.remark-approved,
    &.remark-completed {
      color: #18be6f;
      font-weight: 500;
    }

    &.remark-rejected {
      color: #ff5e4b;
      font-weight: 500;
    }
  }

  .step-connector {
    position: absolute;
    left: 11px;
    top: 24px;
    bottom: -24px;
    width: 2px;
    background-color: #e4e7ed;
    z-index: 1;
  }

  /* 审批操作区域 */
  .approval-actions {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }

  .approval-textarea {
    margin-bottom: 16px;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 0;
  }

  .reject-select {
    width: 200px;
  }

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-textarea__inner) {
    border-radius: 6px;
    font-size: 14px;
  }

  :deep(.el-button) {
    border-radius: 6px;
    font-weight: 500;
  }

  /* 产品标题样式 */
  .title-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .title-right {
    display: flex;
    gap: 8px;
  }

  /* 审批弹窗样式 */
  .approval-status-section {
    margin-bottom: 24px;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }

  .approval-status-label {
    font-size: 14px;
    color: #303133;
    margin-bottom: 12px;
    font-weight: 500;
  }

  .approval-radio-group {
    display: flex;
    gap: 24px;

    :deep(.el-radio) {
      margin-right: 0;
    }

    :deep(.el-radio__input.is-checked .el-radio__inner) {
      background-color: #409eff;
      border-color: #409eff;
    }

    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #409eff;
    }
  }

  .approval-remark-section {
    margin-bottom: 0;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    :deep(.el-textarea__inner) {
      border-radius: 6px;
      font-size: 14px;
      resize: none;
    }
  }

  .reject-level-section {
    margin-bottom: 24px;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    :deep(.el-select) {
      width: 100%;
    }
  }

  .approval-remark-label {
    font-size: 14px;
    color: #303133;
    margin-bottom: 12px;
    font-weight: 500;
  }

  .approval-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    .el-button {
      min-width: 80px;
    }
  }

  // 自定义操作按钮样式
  .title-right {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .action-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    border-radius: 8px;
    border: 1px solid;
    background: transparent;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    justify-content: center;
    height: 36px;

    .btn-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }

    // 主要按钮 - 审批（蓝色）
    &--primary {
      border-color: #3977f3;
      color: #3977f3;
      background-color: transparent;

      &:hover {
        background-color: #3977f3;
        color: #ffffff;
      }

      &:active {
        transform: translateY(1px);
      }
    }

    // 次要按钮 - 编辑（灰色）
    &--secondary {
      border-color: #d1d5db;
      color: #6b7280;
      background-color: transparent;

      &:hover {
        border-color: #9ca3af;
        color: #374151;
        background-color: #f9fafb;
      }

      &:active {
        transform: translateY(1px);
      }
    }

    // 禁用状态
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
        transform: none;
      }
    }
  }

  // 申请人操作按钮样式
  .action-btn--warning {
    background: #e6a23c;
    color: white;
    border: 1px solid #e6a23c;
    width: auto;

    &:hover {
      background: #ebb563;
      border-color: #ebb563;
    }
  }

  .action-btn--danger {
    background: #ff5e4b;
    color: white;
    border: 1px solid #ff5e4b;
    width: auto;

    &:hover {
      background: #f78989;
      border-color: #f78989;
    }
  }

  .action-btn--info {
    background: #909399;
    color: white;
    border: 1px solid #909399;
    width: auto;

    &:hover {
      background: #a6a9ad;
      border-color: #a6a9ad;
    }
  }

  // 重新提交表单样式
  .resubmit-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .el-textarea__inner {
      resize: vertical;
    }
  }

  // 特采详情显示样式
  .special-details-display {
    display: flex;
    align-items: center;

    .value {
      flex: 1;
      min-height: 32px;
      line-height: 32px;
    }
  }
</style>

<style lang="scss">
  .firefly-dialog {
    .el-dialog__header {
      position: relative;
      padding: 13px 20px 0px 20px;
      margin: 0px;
    }
  }
</style>
