/*
 * @Description: 特采申请单常量定义
 * @Version: 1.0
 * @Date: 2025-01-25 16:50:00
 */

// 审批状态类型
export type ApprovalStatusType =
  | 'pending'
  | 'step1'
  | 'step2'
  | 'step3'
  | 'step4'
  | 'approved'
  | 'rejected'
  | 'cancelled'
  | 'returned'

// Element Plus 标签类型
export type TagType = 'primary' | 'success' | 'warning' | 'danger' | 'info'

// 审批操作类型
export type ApprovalActionType = 'approve' | 'reject'

// 审批流程步骤接口
export interface ApprovalFlowStep {
  step: number
  name: string
  approver_id: number
  approver_name: string
  department: string
  status_key: ApprovalStatusType
}

// 驳回层级接口
export interface RejectLevel {
  value: number
  label: string
  step: number
}

// 分页配置接口
export interface PaginationConfig {
  PAGE: number
  LIMIT: number
  PAGE_SIZES: number[]
}

// 审批状态常量
export const APPROVAL_STATUS = {
  PENDING: 'pending',
  STEP1: 'step1',
  STEP2: 'step2',
  STEP3: 'step3',
  STEP4: 'step4',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  CANCELLED: 'cancelled',
  RETURNED: 'returned',
} as const

// 状态文本映射
export const STATUS_TEXT_MAP: Record<ApprovalStatusType, string> = {
  [APPROVAL_STATUS.PENDING]: '申请中',
  [APPROVAL_STATUS.STEP1]: '申请中',
  [APPROVAL_STATUS.STEP2]: '申请中',
  [APPROVAL_STATUS.STEP3]: '申请中',
  [APPROVAL_STATUS.STEP4]: '申请中',
  [APPROVAL_STATUS.APPROVED]: '已通过',
  [APPROVAL_STATUS.REJECTED]: '已驳回',
  [APPROVAL_STATUS.CANCELLED]: '已取消',
  [APPROVAL_STATUS.RETURNED]: '已退回',
}

// 状态标签类型映射
export const STATUS_TAG_TYPE_MAP: Record<ApprovalStatusType, TagType> = {
  [APPROVAL_STATUS.PENDING]: 'primary',
  [APPROVAL_STATUS.STEP1]: 'primary',
  [APPROVAL_STATUS.STEP2]: 'primary',
  [APPROVAL_STATUS.STEP3]: 'primary',
  [APPROVAL_STATUS.STEP4]: 'primary',
  [APPROVAL_STATUS.APPROVED]: 'success',
  [APPROVAL_STATUS.REJECTED]: 'danger',
  [APPROVAL_STATUS.CANCELLED]: 'info',
  [APPROVAL_STATUS.RETURNED]: 'warning',
}

// 分页默认配置
export const PAGINATION_CONFIG: PaginationConfig = {
  PAGE: 1,
  LIMIT: 20,
  PAGE_SIZES: [10, 20, 50, 100],
}

// 日期格式
export const DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss'
export const DATE_ONLY_FORMAT = 'YYYY-MM-DD'

// 审批流程配置
export const APPROVAL_FLOW: ApprovalFlowStep[] = [
  {
    step: 1,
    name: '采购审批',
    approver_id: 101,
    approver_name: '王婷',
    department: '采购部',
    status_key: 'step1',
  },
  {
    step: 2,
    name: '品质经理审批',
    approver_id: 102,
    approver_name: '胡宏志',
    department: '品质部',
    status_key: 'step2',
  },
  {
    step: 3,
    name: 'PMC审批',
    approver_id: 103,
    approver_name: '黄丝丝',
    department: 'PMC部',
    status_key: 'step3',
  },
  {
    step: 4,
    name: '副总经理审批',
    approver_id: 104,
    approver_name: '文总',
    department: '总经理办公室',
    status_key: 'step4',
  },
]

// 驳回层级选择
export const REJECT_LEVELS: RejectLevel[] = [
  { value: 0, label: '驳回到申请人', step: 0 },
  { value: 1, label: '驳回到采购', step: 1 },
  { value: 2, label: '驳回到品质经理', step: 2 },
  { value: 3, label: '驳回到PMC', step: 3 },
]

// 审批操作类型
export const APPROVAL_ACTIONS = {
  APPROVE: 'approve',
  REJECT: 'reject',
} as const

// 根据状态获取当前步骤
export const getCurrentStepByStatus = (status: ApprovalStatusType): number => {
  const stepMap: Record<ApprovalStatusType, number> = {
    pending: 0,
    step1: 1,
    step2: 2,
    step3: 3,
    step4: 4,
    approved: 5,
    rejected: -1,
    cancelled: -2,
    returned: 99,
  }
  return stepMap[status] || 0
}

// 获取状态标签类型
export const getStatusTagType = (status: ApprovalStatusType): TagType => {
  return STATUS_TAG_TYPE_MAP[status] || 'info'
}

// 获取状态文本
export const getStatusText = (status: ApprovalStatusType): string => {
  return STATUS_TEXT_MAP[status] || '未知状态'
}
