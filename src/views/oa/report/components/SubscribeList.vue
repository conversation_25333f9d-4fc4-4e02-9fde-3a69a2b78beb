<template>
  <div>
    <firefly-dialog
      v-model="state.dialogFormVisible"
      :title="state.title"
      width="900px"
      height="700px"
      :close-on-click-modal="false"
      @close="close"
      :loading="state.isLoading"
    >
      <el-form
        inline
        label-width="80px"
        :model="state.queryForm"
        @submit.prevent
        ref="queryFormRef"
        style="padding: 0px 10px"
      >
        <vab-query-form>
          <vab-query-form-left-panel>
            <el-form-item>
              <el-input
                v-model="state.queryForm.filter.orKeywords"
                placeholder="名称/邮件索搜"
                clearable
                @input="changeSearch"
                :prefix-icon="Search"
                :style="{ width: '160px' }"
              />
            </el-form-item>
          </vab-query-form-left-panel>
        </vab-query-form>
      </el-form>
      <div style="height: 600px">
        <el-table
          style="height: 100%"
          v-loading="state.listLoading"
          border
          :data="state.list"
          @selection-change="setSelectRows"
          stripe
        >
          <el-table-column
            type="index"
            align="center"
            width="60"
            label="序号"
          />
          <el-table-column
            align="center"
            label="名称"
            prop="name"
            show-overflow-tooltip
            min-width="100px"
          />
          <el-table-column
            align="center"
            label="操作"
            prop="see"
            width="100"
            show-overflow-tooltip
            min-width="100px"
          >
            <template #default="{ row }">
              <el-button text type="danger" @click="handleUserSubscribe(row)">
                取消关注
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        background
        :current-page="state.queryForm.pageNo"
        :layout="state.layout"
        :page-size="state.queryForm.pageSize"
        :total="state.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </firefly-dialog>
  </div>
</template>

<script setup>
  import { Search } from '@element-plus/icons-vue'
  import { doSubscribe, getSubscribeList } from '@/api/userSubscribeUser'
  import { useUserStore } from '@/store/modules/user'
  const $baseMessage = inject('$baseMessage')
  const emits = defineEmits(['fetch-data'])
  const userStore = useUserStore()
  const { user_id, roles } = storeToRefs(userStore)
  const $baseConfirm = inject('$baseConfirm')

  const state = reactive({
    isLoading: false,
    listLoading: false,
    title: '关注人员列表',
    dialogFormVisible: false,
    changeSearchTimer: null,
    form: {},
    layout: 'total, sizes, prev, pager, next, jumper',
    queryForm: {
      filter: {
        user_id: user_id.value,
        module: 'week_report',
        orKeywords: null,
      },
      op: { orKeywords: 'name,biz_mail' },
      pageNo: 1,
      pageSize: 20,
      sort: 'user_subscribe_user.created_at',
      order: 'desc',
    },
    list: [],
    total: 0,
  })
  const formRef = ref()
  const prodNameSelectRefs = ref([])

  const close = () => {
    state.dialogFormVisible = false
    state.queryForm.pageNo = 1
  }

  const setSelectRows = (val) => {
    state.selectRows = val
  }

  /**
   * 处理操作关注人员
   */
  const handleUserSubscribe = (row) => {
    const action = 0
    const tips =
      action == 1 ? `是否关注 ${row.name} ?` : `是否取消关注 ${row.name} ?`
    $baseConfirm(tips, null, async () => {
      let param = {
        user_id: user_id.value,
        subscribed_user_id: row.id,
        module: 'week_report',
        action: action,
      }
      const { data, msg } = await doSubscribe(param)
      if (data == 1) {
        state.list = state.list.filter((item) => {
          return item.id != row.id
        })
        $baseMessage(msg, 'success', 'vab-hey-message-success')
      }
    })
  }

  const changeSearch = () => {
    if (state.changeSearchTimer) {
      clearTimeout(state.changeSearchTimer)
      state.changeSearchTimer = null
    }
    state.queryForm.pageNo = 1
    state.changeSearchTimer = setTimeout(() => {
      fetchData()
    }, 880)
  }

  const fetchData = () => {
    state.listLoading = true
    getSubscribeList(state.queryForm).then((res) => {
      if (res.data) {
        state.list = res.data.data
        state.total = res.data.total
        state.listLoading = false
      }
    })
  }

  const handleCurrentChange = (val) => {
    state.queryForm.pageNo = val
    fetchData()
  }

  const handleSizeChange = (val) => {
    state.queryForm.pageSize = val
    fetchData()
  }

  const showEdit = async (row) => {
    state.dialogFormVisible = true

    fetchData()
  }

  onMounted(() => {})

  //暴露供父组件使用
  defineExpose({ showEdit })
</script>
