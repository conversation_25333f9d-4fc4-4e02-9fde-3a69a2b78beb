<template>
  <div class="factory-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-form inline :model="queryForm" @submit.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.filter.name"
              clearable
              placeholder="请输入工厂名称"
              @input="queryData"
            />
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button :icon="Plus" type="primary" @click="handleEdit($event)">
          添加
        </el-button>
        <el-button :icon="Delete" type="danger" @click="handleDelete($event)">
          批量删除
        </el-button>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="listLoading"
      border
      :data="list"
      @selection-change="setSelectRows"
      style="width: 100%"
    >
      <el-table-column align="center" show-overflow-tooltip type="selection" />
      <el-table-column
        align="center"
        label="ID"
        prop="id"
        show-overflow-tooltip
        width="80"
      />
      <el-table-column
        align="left"
        label="工厂编码"
        prop="code"
        show-overflow-tooltip
        width="150"
      />
      <el-table-column
        align="left"
        label="工厂名称"
        prop="name"
        show-overflow-tooltip
        min-width="200"
      />
      <el-table-column
        align="left"
        label="关联用户"
        prop="user_names"
        show-overflow-tooltip
        min-width="180"
      />
      <el-table-column
        align="center"
        label="状态"
        prop="status"
        show-overflow-tooltip
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="160"
      >
        <template #default="{ row }">
          <el-button text type="primary" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button text type="primary" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty class="vab-data-empty" description="暂无数据" />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="queryForm.pageNo"
      :layout="layout"
      :page-size="queryForm.limit"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <Edit ref="editRef" @fetch-data="fetchData" />
  </div>
</template>

<script setup>
  import { doDelete, getList } from '@/api/productionFactory'
  import Edit from './components/Edit.vue'
  import { Plus, Delete } from '@element-plus/icons-vue'

  const $baseConfirm = inject('$baseConfirm')
  const $baseMessage = inject('$baseMessage')

  const state = reactive({
    editRef: null,
    list: [],
    listLoading: true,
    layout: 'total, sizes, prev, pager, next, jumper',
    total: 0,
    selectRows: '',
    queryForm: {
      pageNo: 1,
      limit: 10,
      filter: {
        name: '',
      },
      op: {
        name: 'LIKE',
      },
    },
  })

  const editRef = ref()

  const setSelectRows = (val) => {
    state.selectRows = val
  }
  const handleEdit = (row) => {
    if (row && row.id) {
      editRef.value.showEdit(row)
    } else {
      editRef.value.showEdit()
    }
  }
  const handleDelete = (row) => {
    if (row && row.id) {
      $baseConfirm('你确定要删除当前项吗', null, async () => {
        const { msg } = await doDelete({ ids: row.id })
        $baseMessage(msg, 'success', 'vab-hey-message-success')
        await fetchData()
      })
    } else {
      if (state.selectRows.length > 0) {
        const ids = state.selectRows.map((item) => item.id).join()
        $baseConfirm('你确定要删除选中项吗', null, async () => {
          const { msg } = await doDelete({ ids })
          $baseMessage(msg, 'success', 'vab-hey-message-success')
          await fetchData()
        })
      } else {
        $baseMessage('未选中任何行', 'error', 'vab-hey-message-error')
      }
    }
  }
  const handleSizeChange = (val) => {
    state.queryForm.limit = val
    fetchData()
  }
  const handleCurrentChange = (val) => {
    state.queryForm.pageNo = val
    fetchData()
  }
  const queryData = () => {
    state.queryForm.pageNo = 1
    fetchData()
  }
  const fetchData = async () => {
    state.listLoading = true
    const {
      data: { data, total },
    } = await getList(state.queryForm)
    state.list = data
    state.total = total
    state.listLoading = false
  }

  // 解构state中的属性供模板使用
  const { list, listLoading, layout, total, queryForm } = toRefs(state)

  onMounted(() => {
    fetchData()
  })
</script>
