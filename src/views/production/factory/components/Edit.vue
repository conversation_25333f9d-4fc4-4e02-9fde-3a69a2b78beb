<template>
  <firefly-dialog
    v-model="state.dialogFormVisible"
    :title="state.title"
    show-default-button
    width="600px"
    height="calc(100vh - 180px)"
    @close="close"
    @confirm="save"
  >
    <el-form
      ref="formRef"
      label-width="100px"
      :model="state.form"
      :rules="state.rules"
    >
      <el-form-item label="工厂编码" prop="code">
        <el-input v-model="state.form.code" placeholder="请输入工厂编码" />
      </el-form-item>
      <el-form-item label="工厂名称" prop="name">
        <el-input v-model="state.form.name" placeholder="请输入工厂名称" />
      </el-form-item>
      <el-form-item label="关联用户" prop="user_ids">
        <PersonnelSelect
          v-model:value="state.form.user_ids"
          :option-list="state.chargeList"
          :multiple="true"
          :collapse-tags="true"
          placeholder="选择关联用户"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="state.form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="2">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </firefly-dialog>
</template>

<script setup>
  import { doEdit } from '@/api/productionFactory'
  import { getUserThirdList } from '@/api/user'
  import PersonnelSelect from '@/components/PersonnelSelect.vue'

  const $baseMessage = inject('$baseMessage')

  const state = reactive({
    form: {
      code: '',
      name: '',
      status: 1,
      user_ids: [], // 关联用户
    },
    rules: {
      code: [{ required: true, trigger: 'blur', message: '请输入工厂编码' }],
      name: [{ required: true, trigger: 'blur', message: '请输入工厂名称' }],
    },
    title: '',
    dialogFormVisible: false,
    chargeList: [], // 人员列表
  })

  const formRef = ref(null)
  const emit = defineEmits(['fetch-data'])

  const showEdit = (row) => {
    state.dialogFormVisible = true
    if (row) {
      state.title = '编辑工厂'
      state.form = { ...row }
    } else {
      state.title = '添加工厂'
      state.form = {
        code: '',
        name: '',
        status: 1,
        user_ids: [],
      }
    }
    // 加载人员列表
    getChargeList()
  }

  // 加载人员列表
  const getChargeList = async () => {
    try {
      let params = { third: 'redmine' }
      const { data } = await getUserThirdList(params)
      state.chargeList = Object.assign([], data)
    } catch (error) {
      console.error('加载人员列表失败:', error)
    }
  }

  const close = () => {
    state.dialogFormVisible = false
    formRef.value?.resetFields()
  }

  const save = () => {
    formRef.value?.validate(async (valid) => {
      if (valid) {
        const { msg } = await doEdit(state.form)
        $baseMessage(msg, 'success', 'vab-hey-message-success')
        emit('fetch-data')
        close()
      }
    })
  }

  defineExpose({
    showEdit,
  })
</script>
