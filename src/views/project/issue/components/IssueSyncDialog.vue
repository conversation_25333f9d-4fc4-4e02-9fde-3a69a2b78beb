<template>
  <firefly-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="700px"
    height="264px"
  >
    <el-alert
      title="关联事项提示"
      type="info"
      show-icon
      :closable="false"
      class="mb-4"
    >
      <template #default>
        <p>
          • 当前页面为事项{{
            '#' + props.sourceIssue.id
          }}的详情页，该事项为关联关系的
          {{ relationData.is_source ? '源' : '目标' }}事项
        </p>
        <p>
          • 当前选中行事项{{ '#' + currentRow.id }}，该事项为关联关系的
          {{ !relationData.is_source ? '源' : '目标' }}事项
        </p>
      </template>
    </el-alert>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="sync-dialog-form"
    >
      <el-form-item label="同步方向" prop="syncDirection">
        <el-radio-group v-model="formData.syncDirection">
          <el-radio label="source_to_target">源到目标</el-radio>
          <el-radio label="target_to_source">目标到源</el-radio>
          <el-radio label="bidirectional">双向同步</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="同步字段22" prop="syncFields">
        <el-checkbox-group v-model="formData.syncFields">
          <el-checkbox label="subject">标题</el-checkbox>
          <el-checkbox label="description">描述</el-checkbox>
          <el-checkbox label="attachment">附件</el-checkbox>
          <el-checkbox label="status_id">状态</el-checkbox>
          <el-checkbox label="priority_id">优先级</el-checkbox>
          <el-checkbox label="watchers">关注人</el-checkbox>
          <el-checkbox label="assigned">处理人</el-checkbox>
          <el-checkbox label="start_date">开始时间</el-checkbox>
          <el-checkbox label="due_date">结束时间</el-checkbox>
          <el-checkbox label="tracker_id">事项类型</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ mode === 'create' ? '创建' : '更新' }}
        </el-button>
      </span>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import { createSyncRelation, updateSyncRelation } from '@/api/projectIssue'

  const $baseMessage = inject('$baseMessage')

  const props = defineProps({
    sourceIssue: {
      type: Object,
      default: () => ({}),
    },
  })

  const emit = defineEmits(['sync-created', 'sync-updated'])

  const dialogVisible = ref(false)
  const mode = ref('create') // 'create' or 'edit'
  const currentRow = ref(null)
  const relationData = ref(null)
  const relationId = ref(null)

  const formRef = ref(null)
  const formData = reactive({
    sourceIssueId: '', // 添加源事项ID
    targetIssueId: '',
    syncDirection: 'bidirectional',
    syncFields: ['subject', 'description', 'status_id', 'priority_id'],
  })

  const dialogTitle = computed(() => {
    return mode.value === 'create' ? '创建同步关系' : '编辑同步关系'
  })

  const rules = {
    targetIssueId: [
      { required: true, message: '请输入目标事项ID', trigger: 'blur' },
      { type: 'number', message: '请输入有效的事项ID', trigger: 'blur' },
    ],
    syncDirection: [
      { required: true, message: '请选择同步方向', trigger: 'change' },
    ],
    syncFields: [
      {
        type: 'array',
        required: true,
        message: '请至少选择一个同步字段',
        trigger: 'change',
      },
    ],
  }

  const handleClose = () => {
    formRef.value?.resetFields()
    dialogVisible.value = false
    currentRow.value = null
    relationId.value = null
  }

  const handleSubmit = async () => {
    await formRef.value?.validate(async (valid) => {
      if (valid) {
        try {
          const params = {
            source_issue_id: formData.sourceIssueId || props.sourceIssue.id,
            target_issue_id: formData.targetIssueId || currentRow.value?.id,
            sync_direction: formData.syncDirection,
            sync_fields: formData.syncFields,
          }

          if (mode.value === 'create') {
            const { data } = await createSyncRelation(params)
            $baseMessage(
              '同步关系创建成功',
              'success',
              'vab-hey-message-success'
            )
            emit('sync-created', data)
          } else {
            const { data } = await updateSyncRelation({
              relation_id: relationId.value,
              ...params,
            })

            emit('sync-updated', data)
          }

          handleClose()
        } catch (error) {
          $baseMessage(
            error.message || '操作失败',
            'error',
            'vab-hey-message-error'
          )
        }
      }
    })
  }

  // 打开弹窗
  const open = (row = null, syncRelation = null, editMode = true) => {
    currentRow.value = row
    relationData.value = syncRelation
    console.log('relationData:', relationData.value)

    if (syncRelation && editMode) {
      // 编辑模式
      mode.value = 'edit'
      relationId.value = syncRelation.id
      // 根据当前事项是源还是目标来设置表单数据
      if (syncRelation.is_source) {
        formData.sourceIssueId = props.sourceIssue?.id || ''
        formData.targetIssueId = row?.id || syncRelation.target_issue_id
      } else {
        formData.sourceIssueId = row?.id || syncRelation.source_issue_id
        formData.targetIssueId = props.sourceIssue?.id || ''
      }
      formData.syncDirection = syncRelation.sync_direction
      formData.syncFields = syncRelation.field_keys || []
    } else {
      // 创建模式
      mode.value = 'create'
      // 重置表单
      formData.sourceIssueId = props.sourceIssue?.id || ''
      formData.targetIssueId = row?.id || ''
      formData.syncDirection = 'bidirectional'
      formData.syncFields = [
        'subject',
        'description',
        'status_id',
        'priority_id',
      ]
    }

    dialogVisible.value = true
  }

  defineExpose({
    open,
  })
</script>

<style lang="scss" scoped>
  .sync-dialog-form {
    :deep(.el-checkbox-group) {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 12px;
    }
  }
</style>
