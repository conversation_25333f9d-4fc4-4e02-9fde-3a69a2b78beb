<template>
  <div v-if="visible" class="issue-print-dialog" @click.self="handleClose">
    <div class="dialog-content">
      <!-- 打印内容区域 -->
      <div class="print-content-wrapper">
        <div class="print-content" id="print-content">
          <!-- 事项头部 -->
          <div class="issue-header">
            <!-- 操作按钮（不打印） -->
            <div class="header-actions">
              <el-button type="text" @click="handlePrint">
                <vab-icon icon="printer" is-custom-svg />
                立即打印（导出PDF）
              </el-button>
              <el-button @click="handleClose" type="text" style="color: #999">
                <vab-icon icon="close" is-custom-svg />
              </el-button>
            </div>
            <div class="issue-numbers">
              <!-- 父事项编号 -->
              <div v-if="issueData.parent_id" class="parent-issue">
                <TrackerTypeTag
                  v-if="hasTrackerIdentifier(getParentTracker())"
                  :name="getParentTracker()?.name"
                  :tracker-type="getParentTracker()?.tracker_type"
                  :color="getParentTracker()?.color"
                  :identifier="getParentTracker()?.identifier"
                  variant="filled"
                  size="small"
                  :show-identifier="true"
                  :show-name="false"
                />
                <CommonIcon v-else :issue-type="getParentIssueType()" />
                <span class="issue-number">#{{ issueData.parent_id }}</span>
              </div>

              <!-- 箭头分隔符 -->
              <div v-if="issueData.parent_id" class="arrow-separator">/</div>

              <!-- 当前事项编号 -->
              <div class="current-issue">
                <TrackerTypeTag
                  v-if="hasTrackerIdentifier(issueData.tracker)"
                  :name="issueData.tracker?.name"
                  :tracker-type="issueData.tracker?.tracker_type"
                  :color="issueData.tracker?.color"
                  :identifier="issueData.tracker?.identifier"
                  variant="filled"
                  size="small"
                  :show-identifier="true"
                  :show-name="false"
                />
                <CommonIcon v-else :issue-type="getCurrentIssueType()" />
                <span class="issue-number">#{{ issueData.id }}</span>
              </div>
            </div>
          </div>

          <!-- 事项标题 -->
          <div class="issue-title">
            {{ issueData.subject || issueData.title || '无标题' }}
          </div>

          <!-- 分隔线 -->
          <div class="divider"></div>

          <!-- 基础信息标题 -->
          <div class="section-title">基础信息</div>

          <!-- 基础信息两列布局 -->
          <div class="basic-info">
            <div class="info-grid">
              <!-- 左列 -->
              <div class="info-column">
                <div class="info-item">
                  <span class="label">所属项目</span>
                  <span class="value">
                    {{ issueData.project_text?.name || '未知项目' }}
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">状态</span>
                  <span class="value">
                    <el-tag
                      v-if="issueData.issue_status?.name"
                      size="small"
                      :type="getStatusTagType()"
                    >
                      {{ issueData.issue_status?.name }}
                    </el-tag>
                    <span v-else>--</span>
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">父需求</span>
                  <span class="value">{{ getParentName() }}</span>
                </div>
                <div class="info-item">
                  <span class="label">分类</span>
                  <span class="value">{{ getCategoryName() }}</span>
                </div>
                <div class="info-item">
                  <span class="label">优先级</span>
                  <span class="value">
                    <el-tag
                      v-if="issueData.priority_text"
                      size="small"
                      :type="getPriorityTagType(issueData.priority_text)"
                    >
                      {{ issueData.priority_text }}
                    </el-tag>
                    <span v-else>--</span>
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">迭代版本</span>
                  <span class="value">{{ getVersionName() }}</span>
                </div>
                <div class="info-item">
                  <span class="label">创建人</span>
                  <span class="value">
                    {{
                      issueData.author_text?.name ||
                      issueData.author?.name ||
                      '--'
                    }}
                  </span>
                </div>
              </div>

              <!-- 右列 -->
              <div class="info-column">
                <div class="info-item">
                  <span class="label">处理人</span>
                  <span class="value">{{ getAssignedName() }}</span>
                </div>
                <div class="info-item">
                  <span class="label">关注人</span>
                  <span class="value">{{ getWatchersText() }}</span>
                </div>
                <div class="info-item">
                  <span class="label">项目模块</span>
                  <span class="value">{{ getModuleName() }}</span>
                </div>
                <div class="info-item">
                  <span class="label">开始时间</span>
                  <span class="value">
                    {{ formatDate(issueData.start_date) }}
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">结束时间</span>
                  <span class="value">
                    {{ formatDate(issueData.due_date || issueData.end_date) }}
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">是否公开</span>
                  <span class="value">{{ getPrivacyText() }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="divider2"></div>

          <!-- 详情描述 -->
          <div class="section-title">详情描述</div>
          <div class="description-content">
            <div
              v-if="
                issueData.issue_ext?.description_html ||
                issueData.description_html ||
                issueData.description
              "
              v-html="
                issueData.issue_ext?.description_html ||
                issueData.description_html ||
                issueData.description
              "
            ></div>
            <div v-else class="no-content">暂无描述</div>
          </div>

          <!-- 分隔线 -->
          <div class="divider"></div>

          <!-- 检查清单（可选） -->
          <div class="checklist-section" v-show="checklistData.length > 0">
            <div class="section-title-with-checkbox">
              <el-checkbox v-model="printOptions.includeChecklist">
                检查清单
              </el-checkbox>
            </div>
            <div
              v-show="printOptions.includeChecklist"
              class="checklist-content"
            >
              <div v-if="checklistData.length > 0">
                <el-table
                  class="tables issue-check-list-table"
                  :data="checklistData"
                  row-key="id"
                  style="width: 100%; border-top: 1px solid #dcdfe6"
                >
                  <el-table-column
                    prop="item_title"
                    :label="`标题 (${checklistData.length})`"
                    align="left"
                    show-overflow-tooltip
                  >
                    <template #default="{ row }">
                      <span
                        class="issue-check-list-title-display"
                        style="
                          display: inline-flex;
                          align-items: center;
                          max-width: 100%;
                        "
                      >
                        <el-checkbox
                          :model-value="row.status === 1"
                          class="issue-check-list-item-checkbox"
                          disabled
                        />
                        <span
                          style="
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                          "
                        >
                          {{ row.item_title }}
                        </span>
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>

          <!-- 评论（可选） -->
          <div class="comments-section" v-show="commentsData.length > 0">
            <div class="section-title-with-checkbox">
              <el-checkbox v-model="printOptions.includeComments">
                评论
              </el-checkbox>
            </div>
            <div v-show="printOptions.includeComments" class="comments-content">
              <IssueDescri
                :issue-id="props.issueData.id"
                ref="issueDescriRef"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue'
  import TrackerTypeTag from '@/components/TrackerTypeTag.vue'
  import CommonIcon from '@/components/CommonIcon.vue'
  import IssueDescri from './IssueDescri.vue'
  import { getTrackerIcon, IssueIconParent } from '@/utils/index'
  import { getChecklistList, getChecklistDetail } from '@/api/projectCheckList'

  const props = defineProps({
    issueData: {
      type: Object,
      default: () => ({}),
    },
    issueClassData: {
      type: Array,
      default: () => [],
    },
    versionData: {
      type: Array,
      default: () => [],
    },
    userData: {
      type: Array,
      default: () => [],
    },
    parentIssueData: {
      type: Object,
      default: null,
    },
  })

  const emit = defineEmits(['close'])

  const visible = ref(false)
  const printOptions = reactive({
    includeChecklist: true,
    includeComments: true,
  })

  // 检查清单数据
  const checklistData = ref([])
  // 评论数据状态
  const commentsData = ref([])
  // IssueDescri组件引用
  const issueDescriRef = ref(null)

  // 显示弹窗
  const show = () => {
    visible.value = true
    // 通过API获取检查清单数据
    fetchChecklistData()
    // 检查评论数据
    checkCommentsData()
  }

  // 获取检查清单数据
  const fetchChecklistData = async () => {
    try {
      if (!props.issueData?.id) return

      // 使用与CheckList组件相同的参数格式
      const queryForm = {
        filter: {
          issue_id: props.issueData.id,
        },
        page: 1,
        limit: 1000,
      }

      const response = await getChecklistList(queryForm)

      const checklists = response.data.data || []

      if (checklists.length > 0) {
        // 获取第一个检查清单的详情
        const { data: checklistDetail } = await getChecklistDetail({
          id: checklists[0].id,
        })

        // 按 sort_order 排序检查项
        const sortedItems = (checklistDetail.items || []).sort(
          (a, b) => a.sort_order - b.sort_order
        )

        checklistData.value = sortedItems
      } else {
        checklistData.value = []
      }
    } catch (error) {
      console.error('获取检查清单数据失败:', error)
      checklistData.value = []
    }
  }

  // 检查评论数据
  const checkCommentsData = async () => {
    try {
      // 等待IssueDescri组件加载完成
      await nextTick()

      // 延时确保子组件数据加载完成
      setTimeout(() => {
        if (issueDescriRef.value) {
          // 通过DOM检查是否有评论内容
          const commentsContainer = document.querySelector('.comments-content')
          if (commentsContainer) {
            const replyItems = commentsContainer.querySelectorAll('.reply-item')
            commentsData.value = Array.from(replyItems)
          }
        }
      }, 500) // 给IssueDescri组件1秒时间加载数据
    } catch (error) {
      console.error('检查评论数据失败:', error)
      commentsData.value = []
    }
  }

  // 隐藏弹窗
  const hide = () => {
    visible.value = false
  }

  // 处理关闭
  const handleClose = () => {
    hide()
    emit('close')
  }

  // 处理打印
  const handlePrint = () => {
    nextTick(() => {
      // 创建一个新的窗口用于打印
      const printWindow = window.open('', '_blank')
      const printContent = document.getElementById('print-content')

      if (printWindow && printContent) {
        // 获取当前页面的样式
        const styles = Array.from(document.styleSheets)
          .map((styleSheet) => {
            try {
              return Array.from(styleSheet.cssRules)
                .map((rule) => rule.cssText)
                .join('')
            } catch (e) {
              return ''
            }
          })
          .join('')

        // 1. 收集所有 symbol（vite-plugin-svg-icons 会把它们直接插到 <body> 末尾）
        const symbols = Array.from(document.querySelectorAll('symbol'))
          .map((s) => s.outerHTML)
          .join('')

        // 2. 拼成 sprite，插到打印 HTML
        const sprite = `<svg xmlns="http://www.w3.org/2000/svg" style="position:absolute;width:0;height:0">
          ${symbols}
        </svg>`

        // 构建打印页面HTML
        const printHTML = `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="utf-8">
              <title>打印事项详情</title>
              <style>
                ${styles}

                /* 打印专用样式 */
                body {
                  margin: 0;
                  padding: 20px;
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  font-size: 12px;
                  line-height: 1.4;
                  color: #333;
                }

                .print-content {
                  max-width: none !important;
                  margin: 0 !important;
                  padding: 24px 20px !important;
                  box-shadow: none !important;
                  border-radius: 0 !important;
                  background: white !important;
                }

                /* 根据checkbox状态和数据存在性隐藏内容 */
                .checklist-section {
                  display: ${
                    printOptions.includeChecklist &&
                    checklistData.value.length > 0
                      ? 'block'
                      : 'none'
                  } !important;
                }

                .comments-section {
                  display: ${
                    printOptions.includeComments &&
                    commentsData.value.length > 0
                      ? 'block'
                      : 'none'
                  } !important;
                }

                /* 隐藏操作按钮 */
                .header-actions {
                  display: none !important;
                }

                /* 隐藏checkbox */
                .section-title-with-checkbox .el-checkbox__input {
                  display: none !important;
                }

                .section-title-with-checkbox .el-checkbox__label {
                  padding-left: 0 !important;
                }

                /* 隐藏评论操作按钮 */
                .oper {
                  display: none !important;
                }

                .notes-button-text {
                  display: none !important;
                }

                /* 隐藏IssueDescri组件中的所有操作按钮 */
                .comments-content .post-button-text {
                  display: none !important;
                }

                .comments-content .copy-link-btn {
                  display: none !important;
                }

                /* 其他打印样式优化 */
                .issue-title {
                  font-size: 18px !important;
                  margin-bottom: 20px !important;
                }

                .section-title {
                  font-size: 14px !important;
                  margin-bottom: 12px !important;
                }

                .basic-info .info-grid {
                  gap: 24px !important;
                }

                .basic-info .info-column {
                  gap: 12px !important;
                }

                .basic-info .info-item {
                  gap: 24px !important;
                  height: 32px !important;
                }

                .divider {
                  margin: 16px 0 !important;
                  border-top: 1px solid #ddd !important;
                  height: 0 !important;
                  background: none !important;
                }

                .divider2 {
                  margin: 16px 0 24px 0 !important;
                  border-top: 1px solid #ddd !important;
                  height: 0 !important;
                  background: none !important;
                }

                .issue-check-list-table {
                  font-size: 11px !important;
                }

                .issue-check-list-table th,
                .issue-check-list-table td {
                  padding: 6px 8px !important;
                  font-size: 11px !important;
                }

                .issue-check-list-title-display {
                  font-size: 11px !important;
                }

                .issue-check-list-item-checkbox {
                  transform: scale(0.8);
                }

                .author {
                  font-size: 11px !important;
                }

                .notes-box {
                  font-size: 11px !important;
                }

                .reply-item {
                  font-size: 11px !important;
                  padding: 8px 12px !important;
                }

                .text14-333 {
                  font-size: 11px !important;
                }

                .el-avatar {
                  width: 24px !important;
                  height: 24px !important;
                }

                .el-tag {
                  font-size: 10px !important;
                  padding: 2px 6px !important;
                  border-radius: 3px !important;
                }

                /* 避免分页断开 */
                .info-item,
                .flex.align-items-flex-start,
                .issue-check-list-table tr {
                  page-break-inside: avoid;
                }
              </style>
            </head>
            <body>
              <!-- 关键：把 svg sprite 藏到打印文档里 -->
              ${sprite}
              ${printContent.outerHTML}
            </body>
          </html>
        `
        printWindow.document.write(printHTML)
        printWindow.document.close()

        // 等待内容加载完成后打印
        printWindow.onload = () => {
          printWindow.focus()
          printWindow.print()
          printWindow.close()
        }
      }
    })
  }

  // 检查tracker是否有identifier
  const hasTrackerIdentifier = (tracker) => {
    if (!tracker) return false
    return !!tracker.identifier
  }

  // 获取父事项tracker信息
  const getParentTracker = () => {
    // 优先使用props传入的父事项数据
    if (props.parentIssueData && props.parentIssueData.issue_type) {
      return props.parentIssueData.issue_type
    }

    // 如果有父事项信息，尝试从中获取tracker信息
    if (props.issueData.parent_issue) {
      return props.issueData.parent_issue.issue_type
    }
    return null
  }

  // 获取父事项图标类型
  const getParentIssueType = () => {
    const parentTracker = getParentTracker()
    if (parentTracker) {
      return getTrackerIcon(parentTracker, 'parent')
    }
    return (
      IssueIconParent[props.issueData.parent_tracker_id] || IssueIconParent[12]
    )
  }

  // 获取当前事项图标类型
  const getCurrentIssueType = () => {
    if (props.issueData.tracker) {
      const tracker_typer = props.issueData.parent_id ? 'child' : 'parent'
      return getTrackerIcon(props.issueData.tracker, tracker_typer)
    }
    return IssueIconParent[12] // 默认任务图标
  }

  // 获取状态标签类型
  const getStatusTagType = () => {
    const tracker_type = props.issueData.issue_status.status_category
    // 根据状态文本返回对应的标签类型
    const statusMap = {
      not_started: 'info',
      in_progress: '',
      awaiting_acceptance: 'warning',
      completed: 'success',
    }
    return statusMap[tracker_type]
  }

  // 获取优先级标签类型
  const getPriorityTagType = (priorityText) => {
    const priorityMap = {
      P0: 'danger',
      P1: 'warning',
      P2: 'primary',
      P3: 'info',
    }
    return priorityMap[priorityText] || 'info'
  }

  // 获取父需求名称
  const getParentName = () => {
    // 优先使用props传入的父事项数据
    if (props.parentIssueData) {
      return `#${props.parentIssueData.id} ${props.parentIssueData.subject}`
    }
    // 回退到原有数据
    return props.issueData.parent_name || '--'
  }

  // 获取分类名称
  const getCategoryName = () => {
    // 优先使用API获取的数据
    if (props.issueData.class_id) {
      return getIssueClassName(props.issueData.class_id)
    }
    // 回退到原有数据
    return props.issueData.category_text?.name || '--'
  }

  // 获取版本名称
  const getVersionName = () => {
    // 优先使用API获取的数据
    if (props.issueData.fixed_version_id) {
      return getVersionNameById(props.issueData.fixed_version_id)
    }
  }

  // 获取处理人名称
  const getAssignedName = () => {
    // 优先使用API获取的数据
    if (props.issueData.assigned) {
      return getUserNames(props.issueData.assigned)
    }
  }

  // 获取模块名称
  const getModuleName = () => {
    return (
      props.issueData.category_text?.name ||
      props.issueData.category?.name ||
      '--'
    )
  }

  // 获取隐私设置文本
  const getPrivacyText = () => {
    if (
      props.issueData.is_private === true ||
      props.issueData.is_private === 1
    ) {
      return '否'
    }
    if (
      props.issueData.is_private === false ||
      props.issueData.is_private === 0
    ) {
      return '是'
    }
    return '是' // 默认公开
  }

  // 获取关注人文本
  const getWatchersText = () => {
    // 优先使用API获取的数据
    if (props.issueData.watchers && Array.isArray(props.issueData.watchers)) {
      // 如果watchers是ID数组，使用getUserNames
      if (
        props.issueData.watchers.length > 0 &&
        typeof props.issueData.watchers[0] === 'number'
      ) {
        return getUserNames(props.issueData.watchers)
      }
      // 如果watchers是对象数组，使用原有逻辑
      return props.issueData.watchers
        .map((w) => w.name || w.username)
        .join(', ')
    }
    if (
      props.issueData.watcher_users &&
      Array.isArray(props.issueData.watcher_users)
    ) {
      return props.issueData.watcher_users
        .map((w) => w.name || w.username)
        .join(', ')
    }
    return '--'
  }

  // 格式化日期
  const formatDate = (date) => {
    if (!date) return '--'
    try {
      return new Date(date).toLocaleDateString('zh-CN')
    } catch (error) {
      return '--'
    }
  }

  // 根据class_id获取分类名称
  const getIssueClassName = (classId) => {
    if (!classId || !props.issueClassData.length) return '--'
    const classItem = props.issueClassData.find((item) => item.id === classId)
    return classItem?.name || '--'
  }

  // 根据fixed_version_id获取版本名称
  const getVersionNameById = (versionId) => {
    if (!versionId || !props.versionData.length) return '--'
    const versionItem = props.versionData.find((item) => item.id === versionId)
    return versionItem?.name || '--'
  }

  // 根据用户ID数组获取用户名称
  const getUserNames = (userIds) => {
    if (!userIds || !props.userData.length) return '--'

    const userNames = userIds.map((userId) => {
      // assignOptions的数据结构是 { user_id: xxx, user: { lastname: xxx, firstname: xxx } }
      const userItem = props.userData.find((item) => item.user_id === userId)
      if (userItem && userItem.user) {
        return userItem.user.lastname + userItem.user.firstname
      }
      return `用户${userId}`
    })
    return userNames.join(', ')
  }

  // 暴露方法给父组件
  defineExpose({
    show,
    hide,
  })
</script>

<style lang="scss" scoped>
  .issue-print-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;

    .dialog-content {
      width: 100%;
      height: 100%;
      background: white;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
  }

  .print-content-wrapper {
    flex: 1;
    overflow-y: auto;

    .print-content {
      margin: 0 auto;
      background: white;
      padding: 24px 20px;
    }
  }

  .issue-header {
    margin-bottom: 16px;
    position: relative;

    .header-actions {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      gap: 6px;
      z-index: 10;
      margin-top: 16px;
    }

    .issue-numbers {
      display: flex;
      align-items: center;
      gap: 8px;

      .parent-issue,
      .current-issue {
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .issue-number {
        font-size: 14px;
        font-weight: 600;
        color: #3977f3;
      }

      .arrow-separator {
        font-size: 16px;
        color: #666;
        margin: 0 4px;
      }
    }
  }

  .issue-title {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    line-height: 1.4;
  }

  .divider {
    height: 1px;
    background: #dcdfe6;
    margin: 16px 0;
  }

  .divider2 {
    height: 1px;
    background: #dcdfe6;
    margin: 16px 0 24px 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
  }

  .section-title-with-checkbox {
    margin-bottom: 16px;

    :deep(.el-checkbox) {
      font-size: 16px;
      font-weight: 600;

      .el-checkbox__label {
        color: #333;
      }
    }
  }

  .basic-info {
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32px;
    }

    .info-column {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 32px;
      height: 32px;

      .label {
        width: 80px;
        font-size: 14px;
        color: #666;
        flex-shrink: 0;
      }

      .value {
        flex: 1;
        font-size: 14px;
        color: #333;
        display: flex;
        align-items: center;
      }
    }
  }

  .description-content {
    font-size: 14px;
    line-height: 1.6;
    color: #333;

    :deep(p) {
      margin: 0 0 12px 0;
    }

    :deep(ul),
    :deep(ol) {
      margin: 12px 0;
      padding-left: 24px;
    }

    :deep(blockquote) {
      margin: 12px 0;
      padding: 12px 16px;
      background: #f8f9fa;
      border-left: 4px solid #3977f3;
    }
  }

  .checklist-content,
  .comments-content {
    font-size: 14px;
    color: #333;
  }

  .issue-check-list-table.el-table {
    --el-table-border-color: #dcdfe6;
    --el-table-border: 1px solid #dcdfe6;
  }

  .tables {
    :deep(.el-table th .cell) {
      color: #666 !important;
    }

    :deep(.el-table__header th .cell) {
      color: #666 !important;
    }

    // 设置表格边线颜色
    :deep(.el-table) {
      border: 1px solid #dcdfe6;

      th,
      td {
        border-color: #dcdfe6 !important;
      }

      .el-table__border-left-patch,
      .el-table__border-right-patch {
        border-color: #dcdfe6 !important;
      }
    }
  }

  // 标题显示样式
  .issue-check-list-title-display {
    .issue-check-list-item-checkbox {
      flex-shrink: 0;
      margin-right: 8px;
      // 自定义圆形复选框样式 - 使用默认勾号并加粗
      :deep(.el-checkbox__input) {
        .el-checkbox__inner {
          width: 16px;
          height: 16px;
          background-color: #fff;
          border: 1px solid #dcdfe6;
          border-radius: 50%; // 圆形

          &:hover {
            border-color: #409eff;
          }

          // 保留默认勾号但加粗
          &::after {
            position: absolute;
            top: 1px;
            left: 5px;
            width: 3px;
            height: 8px;
            content: '';
            border: 2px solid #fff; // 加粗勾号线条
            border-top: 0;
            border-left: 0;
            transition: transform 0.15s ease-in 0.05s,
              opacity 0.15s ease-in 0.05s;
            transform: rotate(45deg) scaleY(1);
          }
        }

        // 选中状态
        &.is-checked {
          .el-checkbox__inner {
            background-color: #409eff;
            border-color: #409eff;

            // 勾号显示状态
            &::after {
              opacity: 1;
              transform: rotate(45deg) scaleY(1);
            }
          }
        }

        // 未选中状态
        &:not(.is-checked) {
          .el-checkbox__inner {
            &::after {
              opacity: 0;
              transform: rotate(45deg) scaleY(0);
            }
          }
        }
      }
    }

    // 标题文本样式
    span {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  // 完全复制IssueDescri组件的样式
  .author {
    > span,
    > div {
      margin-right: 10px;
    }
  }

  .notes-box {
    max-width: 94%;
    border-bottom: none;
    .notes {
      display: flex;
      justify-content: space-between;
      padding: 15px;
    }
  }

  .reply-item {
    display: inline-block;
    max-width: 100%;
    padding: 10px 20px;
    overflow: auto;
    background-color: #f7f8fa;
    border-radius: 6px;
  }

  // 通用样式类
  .flex {
    display: flex;
  }

  .align-items-flex-start {
    align-items: flex-start;
  }

  .align-items-center {
    align-items: center;
  }

  .flex-algin-center {
    display: flex;
    align-items: center;
  }

  .text14-333 {
    font-size: 14px;
    color: #333;
  }

  .color-text-secondary {
    color: #999;
  }

  .margin-left-8 {
    margin-left: 8px;
  }

  .margin-top-8 {
    margin-top: 8px;
  }

  .text12-999 {
    font-size: 12px;
    color: #999;
    margin-left: 8px;
  }

  /* 统一隐藏评论区域里的各类按钮/弹窗 */
  ::v-deep(.comments-content) {
    .notes-button-text {
      display: none !important;
    }
    .copy-link-btn {
      display: none !important;
    }
  }

  /* 无内容提示样式 */
  .no-content {
    color: #999;
    font-style: italic;
  }

  // 打印样式
  @media print {
    .issue-print-dialog {
      position: static !important;
      width: auto !important;
      height: auto !important;
      background: none !important;
    }

    .dialog-content {
      width: auto !important;
      height: auto !important;
      box-shadow: none !important;
    }

    .header-actions {
      display: none !important;
    }

    .issue-numbers {
      margin-right: 0 !important;
    }

    .print-content-wrapper {
      padding: 0 !important;
      background: none !important;
      overflow: visible !important;
    }

    .print-content {
      max-width: none !important;
      margin: 0 !important;
      padding: 20px !important;
      box-shadow: none !important;
      border-radius: 0 !important;
    }

    .issue-title {
      font-size: 18px !important;
      margin-bottom: 20px !important;
    }

    .section-title {
      font-size: 14px !important;
      margin-bottom: 12px !important;
    }

    .basic-info .info-grid {
      gap: 24px !important;
    }

    .basic-info .info-column {
      gap: 12px !important;
    }

    .basic-info .info-item {
      gap: 24px !important;
    }

    .divider {
      margin: 16px 0 !important;
    }

    .description-content {
      font-size: 12px !important;
    }

    // 隐藏checkbox
    .section-title-with-checkbox :deep(.el-checkbox__input) {
      display: none !important;
    }

    .section-title-with-checkbox :deep(.el-checkbox__label) {
      padding-left: 0 !important;
    }

    // 检查清单打印样式
    .issue-check-list-table {
      font-size: 11px !important;
    }

    .issue-check-list-table th,
    .issue-check-list-table td {
      padding: 6px 8px !important;
      font-size: 11px !important;
    }

    .issue-check-list-title-display {
      font-size: 11px !important;
    }

    .issue-check-list-item-checkbox {
      transform: scale(0.8);
    }

    // 评论打印样式
    .author {
      font-size: 11px !important;
    }

    .notes-box {
      font-size: 11px !important;
    }

    .reply-item {
      font-size: 11px !important;
      padding: 8px 12px !important;
    }

    .text14-333 {
      font-size: 11px !important;
    }

    .el-avatar {
      width: 24px !important;
      height: 24px !important;
    }

    // 标签打印样式
    :deep(.el-tag) {
      font-size: 10px !important;
      padding: 2px 6px !important;
      border-radius: 3px !important;
    }

    // 避免分页断开
    .info-item,
    .flex.align-items-flex-start,
    .issue-check-list-table tr {
      page-break-inside: avoid;
    }

    // 确保分节符在打印时显示
    .divider {
      border-top: 1px solid #ddd !important;
      height: 0 !important;
      background: none !important;
    }

    // 隐藏调试信息
    .debug-info,
    .comment-debug {
      display: none !important;
    }
  }
</style>
