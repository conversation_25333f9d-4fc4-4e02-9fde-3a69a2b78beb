<template>
  <firefly-dialog
    v-model="visible"
    title="选择事项状态"
    width="520px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    class="status-select-dialog"
    :align-center="true"
  >
    <div class="status-select-content">
      <!-- 优化后的提示信息 -->
      <div class="tip-message">
        <div class="tip-card">
          <div class="tip-icon">
            <el-icon size="16" color="#e6a23c">
              <Warning />
            </el-icon>
          </div>
          <div class="tip-text">
            <div class="tip-title">需要重新选择状态</div>
          </div>
        </div>
      </div>

      <!-- 状态选择区域 -->
      <div class="status-list" v-loading="loading">
        <div class="status-label">
          <el-icon size="14" color="#606266">
            <List />
          </el-icon>
          <span>可选状态</span>
        </div>
        <div class="status-select-wrapper">
          <el-select
            v-model="selectedStatusId"
            placeholder="请选择状态"
            size="large"
            style="width: 100%"
            :disabled="loading"
          >
            <el-option
              v-for="status in statusList"
              :key="status.id"
              :label="status.name"
              :value="status.id"
            >
              <div class="status-option-content">
                <div class="status-option-name">{{ status.name }}</div>
                <div
                  v-if="status.description"
                  class="status-option-description"
                >
                  {{ status.description }}
                </div>
              </div>
            </el-option>
          </el-select>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="saving" size="default">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!selectedStatusId"
          :loading="saving"
          size="default"
        >
          <template #icon v-if="!saving">
            <el-icon><Check /></el-icon>
          </template>
          确认选择
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import { ref, watch, inject } from 'vue'
  import { getInitialStatuses } from '@/api/customWorkflow'
  import { doEdit } from '@/api/projectIssue'
  import { Warning, List, Check } from '@element-plus/icons-vue'
  import FireflyDialog from '~/library/components/FireflyDialog/index.vue'

  const $baseMessage = inject('$baseMessage')

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    projectId: {
      type: Number,
      required: true,
    },
    trackerId: {
      type: Number,
      required: true,
    },
    issueId: {
      type: Number,
      required: true,
    },
    currentStatusId: {
      type: Number,
      default: null,
    },
  })

  const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

  const visible = ref(false)
  const loading = ref(false)
  const saving = ref(false)
  const statusList = ref([])
  const selectedStatusId = ref(null)

  // 监听弹窗显示状态
  watch(
    () => props.modelValue,
    (newVal) => {
      visible.value = newVal
      if (newVal) {
        loadStatusList()
      }
    }
  )

  watch(visible, (newVal) => {
    emit('update:modelValue', newVal)
  })

  // 加载状态列表
  const loadStatusList = async () => {
    loading.value = true
    try {
      const { data } = await getInitialStatuses({
        project_id: props.projectId,
        tracker_id: props.trackerId,
      })
      statusList.value = data || []

      // 默认选择第一个状态
      if (statusList.value.length > 0) {
        selectedStatusId.value = statusList.value[0].id
      }
    } catch (error) {
      console.error('加载状态列表失败:', error)
      $baseMessage('加载状态列表失败', 'error')
    } finally {
      loading.value = false
    }
  }

  // 确认选择
  const handleConfirm = async () => {
    if (!selectedStatusId.value) {
      $baseMessage('请选择一个状态', 'warning')
      return
    }

    const selectedStatus = statusList.value.find(
      (s) => s.id === selectedStatusId.value
    )

    console.log('🎯 用户确认状态选择:')
    console.log('  - 事项ID:', props.issueId)
    console.log('  - 旧状态ID:', props.currentStatusId)
    console.log('  - 新状态ID:', selectedStatusId.value)
    console.log('  - 新状态名称:', selectedStatus?.name)
    console.log('  - 新状态数据:', selectedStatus)

    saving.value = true
    try {
      // 同时更新事项类型和状态
      console.log('📤 调用 doEdit 更新tracker_id和status_id...')
      const result = await doEdit({
        id: props.issueId,
        tracker_id: props.trackerId,
        status_id: selectedStatusId.value,
      })

      console.log('✅ tracker_id和状态更新成功:', result)

      emit('confirm', {
        statusId: selectedStatusId.value,
        statusName: selectedStatus?.name || '',
        statusData: selectedStatus,
      })

      visible.value = false
      $baseMessage('状态更新成功', 'success')
    } catch (error) {
      console.error('❌ 更新状态失败:', error)
      $baseMessage('更新状态失败', 'error')
    } finally {
      saving.value = false
    }
  }

  // 取消选择
  const handleCancel = () => {
    emit('cancel')
    visible.value = false
  }
</script>

<style lang="scss" scoped>
  // 弹窗整体样式
  :deep(.status-select-dialog) {
    .el-dialog__body {
      padding: 24px;
    }

    .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid #f0f0f0;
      background-color: #fafafa;
    }
  }

  // 内容区域
  .status-select-content {
    .tip-message {
      margin-bottom: 24px;

      .tip-card {
        display: flex;
        align-items: flex-start;
        padding: 16px;
        background: #fdf6ec;
        border: 1px solid #faecd8;
        border-radius: 8px;
        border-left: 4px solid #e6a23c;

        .tip-icon {
          margin-right: 12px;
          margin-top: 2px;
        }

        .tip-text {
          flex: 1;

          .tip-title {
            font-size: 14px;
            font-weight: 600;
            color: #e6a23c;
            margin-bottom: 4px;
            line-height: 1.2;
          }

          .tip-desc {
            font-size: 13px;
            color: #b88230;
            line-height: 1.4;
          }
        }
      }
    }

    .status-list {
      .status-label {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #303133;
      }

      .status-select-wrapper {
        margin-top: 8px;

        .el-select {
          .el-input__wrapper {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
            }

            &.is-focus {
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            }
          }
        }
      }
    }
  }

  // 下拉选项样式
  :deep(.el-select-dropdown) {
    .el-select-dropdown__item {
      padding: 12px 16px;

      .status-option-content {
        .status-option-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          line-height: 1.2;
          margin-bottom: 2px;
        }

        .status-option-description {
          font-size: 12px;
          color: #909399;
          line-height: 1.3;
        }
      }

      &:hover {
        background-color: #f0f9ff;
      }

      &.selected {
        background-color: #ecf5ff;
        color: #409eff;

        .status-option-content .status-option-name {
          color: #409eff;
          font-weight: 600;
        }
      }
    }
  }

  // 底部按钮
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    .el-button {
      padding: 10px 20px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:not(.is-disabled):hover {
        transform: translateY(-1px);
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.status-select-dialog) {
      .el-dialog {
        width: 90% !important;
        margin: 5vh auto !important;
      }
    }

    .status-select-content {
      .status-list {
        .status-select-wrapper {
          .el-select {
            .el-input__wrapper {
              padding: 8px 12px;
            }
          }
        }
      }
    }
  }
</style>
