<template>
  <div class="record-container">
    <div class="search-container">
      <div class="flex-algin-center search-box">
        <img :src="searchImg" alt="" class="img s-img" />
        <input
          type="text"
          v-model="inputValue"
          class="search-input"
          placeholder="输入快递单号搜索"
        />
      </div>
      <div class="setting-btn" @click="showSettingPopup = true">
        <vab-icon
          style="height: 0.53rem; width: 0.53rem"
          icon="setting"
          is-custom-svg
        />
      </div>
    </div>
    <FloatButton>
      <el-upload
        ref="upload"
        class="upload-demo"
        accept="image/jpeg,image/gif,image/png"
        :action="UploadServer"
        :data="state.uploadData"
        :before-upload="imgProgress"
        :on-success="imgSuccess"
        :on-error="imgError"
        :show-file-list="false"
      >
        <template #trigger>
          <img :src="floatImg" alt="" class="dblock f-img" />
        </template>
      </el-upload>
    </FloatButton>
    <div class="pulldown">
      <div class="pulldown-bswrapper" ref="bsWrapper">
        <div class="pulldown-scroller">
          <div class="pulldown-wrapper">
            <div v-show="beforePullDown">
              <span>下拉刷新</span>
            </div>
            <div v-show="!beforePullDown">
              <div v-show="isPullingDown">
                <span>刷新中...</span>
              </div>
              <div v-show="!isPullingDown">
                <span>刷新成功</span>
              </div>
            </div>
          </div>
          <div class="record-list">
            <a
              class="record-item"
              v-for="item in recordList"
              :key="item.operator_id"
              :href="'/#/m/oa/returnOrder/detail/' + item.id"
            >
              <div class="item-odd-number">快递单号：{{ item.barcode }}</div>
              <div class="item-operator">操作人：{{ item.operator.name }}</div>
              <div class="item-time">操作时间：{{ item.updated_at }}</div>
            </a>
          </div>
          <div class="pullup-tips">
            <template v-if="isNoMore">
              <div class="before-trigger">
                <span class="pullup-txt">没有更多数据了</span>
              </div>
            </template>
            <template v-else>
              <div v-if="!isPullUpLoad" class="before-trigger">
                <span class="pullup-txt">上拉加载更多</span>
              </div>
              <div v-else class="after-trigger">
                <span class="pullup-txt">加载中</span>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置选择弹出层 -->
    <div
      v-if="showSettingPopup"
      class="setting-popup-overlay"
      @click="showSettingPopup = false"
    >
      <div class="setting-popup" @click.stop>
        <div class="popup-header">
          <h3>识别模式设置</h3>
          <div class="close-btn" @click="showSettingPopup = false">×</div>
        </div>
        <div class="popup-content">
          <div
            class="setting-option"
            :class="{ active: recognitionMode === 'auto' }"
            @click="setRecognitionMode('auto')"
          >
            <div class="option-title">自动识别</div>
            <div class="option-check" v-if="recognitionMode === 'auto'">✓</div>
          </div>
          <div
            class="setting-option"
            :class="{ active: recognitionMode === 'scan' }"
            @click="setRecognitionMode('scan')"
          >
            <div class="option-title">扫码识别</div>
            <div class="option-check" v-if="recognitionMode === 'scan'">✓</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue'
  import searchImg from '@/assets/icon_images/search.png'
  import floatImg from '@/assets/record_images/float.png'
  import { mobileRem } from '@/utils/common'
  import {
    getList,
    RecordInfo,
    UploadServer,
    AddRecord,
    getSearchbarcode,
  } from '@/api/oaReturnOrderExpress'
  import FloatButton from './floatbutton.vue'
  import { useRouter } from 'vue-router'
  import BScroll from '@better-scroll/core'
  import PullDown from '@better-scroll/pull-down'
  import PullUp from '@better-scroll/pull-up'
  import { ElLoading } from 'element-plus'
  BScroll.use(PullDown)
  BScroll.use(PullUp)
  const router = useRouter()
  mobileRem()
  const recordList = reactive([] as Array<RecordInfo>)
  const inputValue = ref('')
  const TIME_BOUNCE = 800
  const THRESHOLD = 70
  const STOP = 56
  let step = ref(1)
  const beforePullDown = ref(true)
  const isPullingDown = ref(false)
  const isPullUpLoad = ref(false)
  const isNoMore = ref(false)
  const bscroll: any = ref('')
  const bsWrapper = ref('')
  const timeout: any = ref(null)
  const $baseMessage: any = inject('$baseMessage')
  const fullscreenLoading: any = ref(null)

  // 设置相关状态
  const showSettingPopup = ref(false)
  const recognitionMode = ref(
    localStorage.getItem('returnOrder_recognitionMode') || 'auto'
  )

  // 上传时传递的额外参数
  const state = reactive({
    uploadData: {
      recognition_mode: recognitionMode.value,
    },
  })

  watch(inputValue, (newValue, oldValue) => {
    if (timeout.value != null) {
      clearTimeout(timeout.value)
      timeout.value = null
    }
    timeout.value = setTimeout(async () => {
      await new Promise((resolve) => setTimeout(resolve, 500))
      getRecoList()
    }, 0)
  })

  const openFullScreen2 = () => {
    fullscreenLoading.value = ElLoading.service({
      lock: true,
      text: 'Loading',
    })
  }

  const imgProgress = (event: any) => {
    openFullScreen2()
  }

  /**
   * OCR图片识别成功后处理
   */
  const imgSuccess = (res: any) => {
    fullscreenLoading.value?.close()
    // if (
    //   typeof res.data.barcode == 'undefined' ||
    //   res.data.barcode == '' ||
    //   res.data.barcode == null
    // ) {
    //   imgError(res)
    //   return
    // }
    // const { barcode, image_full_url }: AddRecord = res.data
    router.push({
      path: '/m/oa/returnOrder/add',
      query: res.data,
    })
  }

  /**
   * OCR图片识别失败处理
   */
  const imgError = (error: any) => {
    fullscreenLoading.value?.close()

    let errorMessage = '识别图片失败请重试'

    // 尝试从各种可能的格式中提取msg
    try {
      // 方式1: 如果error直接有msg属性
      if (error && error.msg) {
        errorMessage = error.msg
      }
      // 方式2: 如果error是对象，尝试转换为字符串并提取JSON中的msg
      else if (typeof error === 'object') {
        const errorStr = String(error)
        const msgMatch = errorStr.match(/"msg"\s*:\s*"([^"]*)"/)
        if (msgMatch && msgMatch[1]) {
          errorMessage = msgMatch[1]
        }
      }
      // 方式3: 如果error本身是字符串，尝试提取JSON中的msg
      else if (typeof error === 'string') {
        const msgMatch = error.match(/"msg"\s*:\s*"([^"]*)"/)
        if (msgMatch && msgMatch[1]) {
          errorMessage = msgMatch[1]
        }
      }
    } catch (e) {
      // 解析失败，使用默认错误信息
    }

    $baseMessage(errorMessage, 'error', 'vab-hey-message-error')
  }

  const getRecoList = (num = 1, size = 10) => {
    if (inputValue.value != '') {
      getSearchbarcode({
        filter: {
          barcode: inputValue.value,
        },
        op: {
          barcode: 'LIKE',
          created_at: 'DATETIME',
        },
      }).then((res) => {
        const { data: responseData } = res.data
        isNoMore.value = responseData.length < 10

        bscroll.value[responseData.length < 10 ? 'closePullUp' : 'openPullUp']()
        if (num == 1) {
          recordList.splice(0, recordList.length, ...responseData)
          beforePullDown.value = false
          isPullingDown.value = true
          isPullingDown.value = false
          finishPullDown()
        } else {
          recordList.push(...responseData)
          bscroll.value.finishPullUp()
          bscroll.value.refresh()
          isPullUpLoad.value = false
        }
        step.value = step.value + 1
      })
    } else {
      getList({ pageNo: num, pageSize: size }).then((res) => {
        const { data: responseData } = res.data
        isNoMore.value = responseData.length < 10
        bscroll.value[responseData.length < 10 ? 'closePullUp' : 'openPullUp']()
        if (num == 1) {
          recordList.splice(0, recordList.length, ...responseData)
          beforePullDown.value = false
          isPullingDown.value = true
          isPullingDown.value = false
          finishPullDown()
        } else {
          recordList.push(...responseData)
          bscroll.value.finishPullUp()
          bscroll.value.refresh()
          isPullUpLoad.value = false
        }
        step.value = step.value + 1
      })
    }
  }

  async function pullingDownHandler() {
    step.value = 1
    getRecoList(step.value, 10)
  }
  async function pullingUpHandler() {
    getRecoList(step.value, 10)
  }
  async function finishPullDown() {
    bscroll.value.finishPullDown()
    await new Promise((resolve) => setTimeout(resolve, TIME_BOUNCE + 100))
    beforePullDown.value = true
    bscroll.value.refresh()
  }

  // 设置识别模式
  const setRecognitionMode = (mode: string) => {
    recognitionMode.value = mode
    localStorage.setItem('returnOrder_recognitionMode', mode)
    showSettingPopup.value = false
    $baseMessage(
      `已切换到${mode === 'auto' ? '自动识别' : '扫码识别'}模式`,
      'success',
      'vab-hey-message-success'
    )
  }

  // 监听识别模式变化，更新上传参数
  watch(recognitionMode, (newMode) => {
    state.uploadData.recognition_mode = newMode
  })

  onMounted(() => {
    getRecoList()
    bscroll.value = new BScroll(bsWrapper.value, {
      scrollY: true,
      bounceTime: TIME_BOUNCE,
      useTransition: false,
      click: true,
      pullDownRefresh: {
        threshold: THRESHOLD,
        stop: STOP,
      },
      pullUpLoad: true,
    })
    bscroll.value.on('pullingDown', pullingDownHandler)
    bscroll.value.on('pullingUp', pullingUpHandler)
  })
</script>
<style lang="scss">
  .record-container {
    background: #f1f1f1;
    padding: 0.4rem 0.43rem;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .img {
    display: block;
  }

  .s-img {
    width: 0.53rem;
    height: 0.53rem;
  }

  .search-container {
    display: flex;
    align-items: center;
    gap: 0.32rem;
    width: 100%;
  }

  .search-box {
    flex: 1;
    padding: 0.11rem 0.43rem;
    background: #fff;
    border-radius: 0.43rem;

    .search-input {
      border: none;
      font-size: 0.37rem;
      padding-left: 0.16rem;
    }
  }

  .setting-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 0.76rem;
    height: 0.76rem;
    background: #fff;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
      background: #f5f5f5;
    }

    .el-icon-setting {
      font-size: 0.53rem;
      color: #666;
    }
  }

  .record-item {
    margin: 0;
    margin-top: 0.32rem;
    padding: 0.32rem 0.35rem;
    border-radius: 0.21rem;
    background: #fff;
    display: block;

    .item-odd-number {
      font-size: 0.37rem;
      color: #000;
      font-weight: bold;
      line-height: 1.5;
    }

    .item-operator {
      padding: 0.08rem 0;
    }

    .item-operator,
    .item-time {
      font-size: 0.32rem;
      color: #999;
      line-height: 1.5;
    }
  }

  .f-img {
    height: 50px;
    height: 50px;
  }

  .pulldown {
    height: 100%;
    overflow: hidden;
  }

  .pulldown-bswrapper {
    position: relative;
    height: 100%;
    overflow: hidden;
  }

  .pulldown-wrapper {
    position: absolute;
    width: 100%;
    padding: 0.53rem;
    box-sizing: border-box;
    transform: translateY(-100%) translateZ(0);
    text-align: center;
    color: #999;
  }

  .pullup-tips {
    padding: 0.53rem;
    text-align: center;
    color: #999;
  }

  /* 设置弹出层样式 */
  .setting-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.53rem;
  }

  .setting-popup {
    background: #fff;
    border-radius: 0.32rem;
    width: 100%;
    max-width: 10.67rem;
    box-shadow: 0 0.11rem 0.53rem rgba(0, 0, 0, 0.2);
    overflow: hidden;
    animation: popup-fade-in 0.3s ease;
  }

  @keyframes popup-fade-in {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.53rem 0.64rem;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      margin: 0;
      font-size: 0.48rem;
      font-weight: 500;
      color: #333;
    }

    .close-btn {
      width: 0.85rem;
      height: 0.85rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.64rem;
      color: #999;
      cursor: pointer;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:active {
        background: #f5f5f5;
        transform: scale(0.9);
      }
    }
  }

  .popup-content {
    padding: 0.43rem 0;
  }

  .setting-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.53rem 0.64rem;
    cursor: pointer;
    transition: background 0.2s ease;

    &:active {
      background: #f8f9fa;
    }

    &.active {
      background: #f0f9ff;
    }

    .option-title {
      font-size: 0.43rem;
      color: #333;
      font-weight: 500;
    }

    .option-check {
      font-size: 0.53rem;
      color: #1890ff;
      font-weight: bold;
    }
  }
</style>
