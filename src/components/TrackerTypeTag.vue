<template>
  <div
    class="tracker-type-tag"
    :class="[size, variant, { clickable }]"
    @click="handleClick"
  >
    <!-- 纯文字样式 -->
    <span
      v-if="variant === 'text'"
      class="text-only"
      :style="{ color: finalColor }"
    >
      {{ name }}
    </span>

    <!-- 标签样式 -->
    <div v-else class="tag-content" :style="getTagStyle">
      <!-- 图标 -->
      <vab-icon
        v-if="showIcon && trackerIcon"
        :icon="trackerIcon"
        class="tracker-icon"
      />

      <!-- 识别符 -->
      <span v-if="showIdentifier" class="identifier">
        {{ finalIdentifier }}
      </span>

      <!-- 首字母 -->
      <span v-if="showFirstLetter" class="identifier">{{ firstLetter }}</span>

      <!-- 名称 -->
      <span v-if="showName" class="name">{{ finalName }}</span>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import {
    getTrackerTypeIcon,
    getTrackerTypeColor,
    getTrackerTypeIdentifier,
  } from '@/config/trackerTypes'

  const props = defineProps({
    // 事项类型数据
    tracker: {
      type: Object,
      default: () => ({}),
    },
    // 或者直接传递属性
    name: {
      type: String,
      default: '',
    },
    trackerType: {
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: '#409EFF',
    },
    identifier: {
      type: String,
      default: '',
    },
    // 显示选项
    showName: {
      type: Boolean,
      default: false,
    },
    showIcon: {
      type: Boolean,
      default: false,
    },
    showIdentifier: {
      type: Boolean,
      default: true,
    },
    showFirstLetter: {
      type: Boolean,
      default: false,
    },
    // 样式变体
    variant: {
      type: String,
      default: 'filled', // filled, text
      validator: (value) => ['filled', 'text'].includes(value),
    },
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: (value) => ['small', 'medium', 'large'].includes(value),
    },
    clickable: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['click'])

  // 计算最终显示的属性
  const finalName = computed(() => {
    return (
      props.name || props.tracker?.name || props.tracker?.tracker_name || ''
    )
  })

  const finalTrackerType = computed(() => {
    return props.trackerType || props.tracker?.tracker_type || ''
  })

  const finalColor = computed(() => {
    return (
      props.color ||
      props.tracker?.color ||
      getDefaultColor(finalTrackerType.value)
    )
  })

  const finalIdentifier = computed(() => {
    return (
      props.identifier ||
      props.tracker?.identifier ||
      getDefaultIdentifier(finalTrackerType.value, finalName.value)
    )
  })

  // 计算首字母
  const firstLetter = computed(() => {
    const identifier = finalIdentifier.value
    if (identifier && identifier.length > 0) {
      return identifier.charAt(0).toUpperCase()
    }
    return ''
  })

  // 获取事项类型图标
  const trackerIcon = computed(() => {
    return getTrackerTypeIcon(finalTrackerType.value)
  })

  // 根据背景色计算文字颜色
  const textColor = computed(() => {
    // 对于纯色块样式，统一使用白色文字
    if (props.variant === 'filled') {
      return '#ffffff'
    }

    // 对于其他样式，使用背景色作为文字颜色
    return finalColor.value || '#409EFF'
  })

  // 计算标签样式
  const getTagStyle = computed(() => {
    // 只有 filled 样式需要样式计算
    return {
      backgroundColor: finalColor.value,
      color: textColor.value,
    }
  })

  // 获取默认颜色（使用统一配置）
  const getDefaultColor = (trackerType) => {
    return getTrackerTypeColor(trackerType)
  }

  // 获取默认识别符（使用统一配置）
  const getDefaultIdentifier = (trackerType, name) => {
    return getTrackerTypeIdentifier(trackerType, name)
  }

  // 点击处理
  const handleClick = () => {
    if (props.clickable) {
      emit('click', {
        name: finalName.value,
        trackerType: finalTrackerType.value,
        color: finalColor.value,
        identifier: finalIdentifier.value,
      })
    }
  }
</script>

<style lang="scss" scoped>
  .tracker-type-tag {
    display: inline-block;

    &.clickable {
      cursor: pointer;

      &:hover .tag-content {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }

    .tag-content {
      display: inline-flex;
      align-items: center;
      border-radius: 4px;
      font-weight: 500;
      transition: all 0.2s ease;
      white-space: nowrap;

      .tracker-icon {
        font-size: inherit;
        margin-right: 4px;
      }

      .identifier {
        font-size: inherit;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-weight: 600;
        letter-spacing: 0;
        line-height: initial;
      }

      .name {
        font-size: inherit;
      }
    }

    // 样式变体
    &.filled .tag-content {
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    // 纯文字样式
    .text-only {
      font-size: inherit;
      font-weight: 500;
    }

    // 尺寸变体
    &.small .tag-content {
      padding: 0 6px;
      font-size: 11px;
      height: 20px;
    }

    &.medium .tag-content {
      padding: 0 8px;
      font-size: 12px;
      height: 24px;
    }

    &.large .tag-content {
      padding: 0 12px;
      font-size: 14px;
      height: 32px;
    }
  }
</style>
