<template>
  <div class="tracker-color-picker">
    <div class="color-preview" @click="showPicker = !showPicker">
      <div class="color-block" :style="{ backgroundColor: modelValue }"></div>
      <span class="color-text">{{ modelValue }}</span>
      <vab-icon icon="arrow-down-s-line" class="arrow-icon" />
    </div>

    <div v-if="showPicker" class="color-picker-panel">
      <!-- 预设颜色 -->
      <div class="preset-colors">
        <!-- <div class="section-title">预设颜色</div> -->
        <div class="color-grid">
          <div
            v-for="color in presetColors"
            :key="color.value"
            class="preset-color-item"
            :class="{ active: modelValue === color.value }"
            @click="selectColor(color.value)"
          >
            <div
              class="color-circle"
              :style="{ backgroundColor: color.value }"
            ></div>
          </div>
        </div>
      </div>

      <!-- 自定义颜色 -->
      <!-- <div class="custom-color">
        <div class="section-title">自定义颜色</div>
        <div class="custom-input">
          <el-input
            v-model="customColor"
            placeholder="输入颜色值 (如: #FF5722)"
            @input="onCustomColorInput"
            @keyup.enter="selectCustomColor"
          >
            <template #prepend>
              <div
                class="custom-preview"
                :style="{
                  backgroundColor: isValidColor(customColor)
                    ? customColor
                    : '#ccc',
                }"
              ></div>
            </template>
          </el-input>
          <el-button
            type="primary"
            size="small"
            @click="selectCustomColor"
            :disabled="!isValidColor(customColor)"
          >
            应用
          </el-button>
        </div>
      </div> -->

      <!-- 提示信息 -->
      <!-- <div class="tip">
        <span class="tip-text">点击颜色直接应用，点击外部区域关闭</span>
      </div> -->
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'

  const props = defineProps({
    modelValue: {
      type: String,
      default: '#409EFF',
    },
  })

  const emit = defineEmits(['update:modelValue', 'change'])

  const showPicker = ref(false)

  // 预设颜色配置
  const presetColors = [
    { value: '#409EFF' }, // 蓝色 - 需求默认色
    { value: '#F56C6C' }, // 红色 - 缺陷默认色
    { value: '#67C23A' }, // 绿色 - 任务默认色
    { value: '#E6A23C' }, // 橙色
    { value: '#9C27B0' }, // 紫色
    { value: '#00BCD4' }, // 青色
    { value: '#E91E63' }, // 粉色
    { value: '#1976D2' }, // 深蓝
    { value: '#388E3C' }, // 深绿
    { value: '#D32F2F' }, // 深红
    { value: '#607D8B' }, // 灰色
    { value: '#795548' }, // 棕色
    { value: '#FF9800' }, // 深橙
    { value: '#673AB7' }, // 深紫
    { value: '#009688' }, // 深青
    { value: '#FFC107' }, // 黄色
    { value: '#8BC34A' }, // 浅绿
    { value: '#FF5722' }, // 深橙红
  ]

  // 选择预设颜色
  const selectColor = (color) => {
    emit('update:modelValue', color)
    emit('change', color)
    // 选择预设颜色后直接关闭面板
    showPicker.value = false
  }

  // 点击外部关闭选择器
  const handleClickOutside = (event) => {
    if (!event.target.closest('.tracker-color-picker')) {
      showPicker.value = false
    }
  }

  // 监听点击外部事件
  document.addEventListener('click', handleClickOutside)
</script>

<style lang="scss" scoped>
  .tracker-color-picker {
    position: relative;
    display: inline-block;

    .color-preview {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
      }

      .color-block {
        width: 20px;
        height: 20px;
        border-radius: 3px;
        border: 1px solid #e4e7ed;
      }

      .color-text {
        font-size: 14px;
        color: #606266;
        min-width: 70px;
      }

      .arrow-icon {
        color: #c0c4cc;
        transition: transform 0.3s;
      }
    }

    .color-picker-panel {
      position: absolute;
      top: 100%;
      left: 0;
      z-index: 1000;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 12px;
      min-width: 280px;
      margin-top: 4px;

      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 12px;
      }

      .preset-colors {
        margin-bottom: 16px;

        .color-grid {
          display: grid;
          grid-template-columns: repeat(9, 1fr);
          gap: 8px;

          .preset-color-item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background-color: #f5f7fa;
            }

            &.active {
              background-color: #ecf5ff;
              border: 2px solid #409eff;
            }

            .color-circle {
              width: 24px;
              height: 24px;
              border-radius: 4px;
              border: 1px solid #e4e7ed;
            }
          }
        }
      }

      .custom-color {
        margin-bottom: 12px;

        .custom-input {
          display: flex;
          gap: 6px;
          align-items: flex-start;

          .el-input {
            flex: 1;
          }

          .custom-preview {
            width: 18px;
            height: 18px;
            border-radius: 3px;
            border: 1px solid #e4e7ed;
          }
        }
      }

      // .tip {
      //   padding-top: 8px;
      //   border-top: 1px solid #e4e7ed;
      //   text-align: center;

      //   .tip-text {
      //     font-size: 12px;
      //     color: #909399;
      //   }
      // }
    }
  }
</style>
