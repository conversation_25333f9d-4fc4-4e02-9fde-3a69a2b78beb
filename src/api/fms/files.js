/**
 * FMS 文件管理 API
 */

import request from '@/utils/request'

const BASE_URL = '/api/fms/files'

/**
 * 获取文件列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const list = (params = {}) => {
  return request({
    url: BASE_URL,
    method: 'GET',
    params,
  })
}

/**
 * 获取单个文件信息
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const show = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET',
  })
}

/**
 * 上传文件
 * @param {Object} data 上传数据
 * @param {Function} onProgress 进度回调
 * @returns {Promise}
 */
export const upload = (data, onProgress) => {
  const formData = new FormData()

  // 添加文件
  formData.append('file', data.file)

  // 添加其他参数
  if (data.directoryId) formData.append('directory_id', data.directoryId)
  if (data.name) formData.append('name', data.name)

  return request({
    url: BASE_URL,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress) {
        const percent = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress({
          loaded: progressEvent.loaded,
          total: progressEvent.total,
          percent: percent,
        })
      }
    },
  })
}

/**
 * 下载文件
 * @param {number} id 文件ID
 * @param {number} version 版本号（可选）
 * @returns {Promise}
 */
export const download = (id, version) => {
  const params = {}
  if (version) params.version = version

  return request({
    url: `${BASE_URL}/${id}/download`,
    method: 'GET',
    params,
    responseType: 'blob',
  })
    .then((response) => {
      console.log('response:', response)
      console.log('response.headers:', response.headers)
      console.log('headers type:', typeof response.headers)
      console.log('headers constructor:', response.headers.constructor.name)

      // 安全地获取响应头和数据
      const headers = response.headers || {}
      const data = response.data

      // 验证响应数据
      if (!data) {
        throw new Error('文件数据为空')
      }

      // 调试：打印所有可用的头部信息
      console.log('所有响应头部:')
      if (headers.get) {
        // AxiosHeaders 对象
        console.log('使用 AxiosHeaders.get() 方法')
        for (const [key, value] of headers) {
          console.log(`${key}: ${value}`)
        }
      } else {
        // 普通对象
        console.log('使用普通对象访问')
        Object.keys(headers).forEach((key) => {
          console.log(`${key}: ${headers[key]}`)
        })
      }

      // 从响应头获取文件名，支持多种编码格式
      // 尝试多种方式获取 content-disposition
      let contentDisposition = null

      // 方法1: 使用 AxiosHeaders 的 get 方法
      if (headers.get && typeof headers.get === 'function') {
        contentDisposition =
          headers.get('content-disposition') ||
          headers.get('Content-Disposition')
        console.log('方法1 - AxiosHeaders.get():', contentDisposition)
      }

      // 方法2: 直接访问属性
      if (!contentDisposition) {
        contentDisposition =
          headers['content-disposition'] || headers['Content-Disposition']
        console.log('方法2 - 直接属性访问:', contentDisposition)
      }

      // 方法3: 遍历所有头部查找（不区分大小写）
      if (!contentDisposition && headers) {
        const headerKeys = Object.keys(headers)
        for (const key of headerKeys) {
          if (key.toLowerCase() === 'content-disposition') {
            contentDisposition = headers[key]
            console.log('方法3 - 不区分大小写查找:', contentDisposition)
            break
          }
        }
      }

      // 方法4: 如果是 AxiosHeaders，尝试遍历
      if (!contentDisposition && headers[Symbol.iterator]) {
        try {
          for (const [key, value] of headers) {
            if (key.toLowerCase() === 'content-disposition') {
              contentDisposition = value
              console.log('方法4 - AxiosHeaders遍历:', contentDisposition)
              break
            }
          }
        } catch (e) {
          console.warn('AxiosHeaders遍历失败:', e)
        }
      }

      let filename = `file_${id}`

      console.log('最终获取的contentDisposition:', contentDisposition)

      if (contentDisposition) {
        // 处理 RFC 5987 编码格式 (filename*=UTF-8''encoded_filename)
        const rfc5987Match = contentDisposition.match(
          /filename\*=UTF-8''([^;]+)/i
        )
        if (rfc5987Match) {
          try {
            filename = decodeURIComponent(rfc5987Match[1])
          } catch (e) {
            console.warn('RFC5987文件名解码失败:', e)
          }
        } else {
          // 处理标准格式 (filename="filename" 或 filename=filename)
          const standardMatch = contentDisposition.match(
            /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/i
          )
          if (standardMatch && standardMatch[1]) {
            filename = standardMatch[1].replace(/['"]/g, '')

            // 尝试多种解码方式
            try {
              // 首先尝试 decodeURIComponent
              if (filename.includes('%')) {
                filename = decodeURIComponent(filename)
              } else {
                // 如果包含乱码字符，尝试从 ISO-8859-1 转换为 UTF-8
                if (/[\u00C0-\u00FF]/.test(filename)) {
                  // 将 ISO-8859-1 字节转换为 UTF-8
                  const bytes = new Uint8Array(filename.length)
                  for (let i = 0; i < filename.length; i++) {
                    bytes[i] = filename.charCodeAt(i)
                  }
                  filename = new TextDecoder('utf-8').decode(bytes)
                }
              }
            } catch (e) {
              console.warn('文件名编码转换失败:', e)
              // 如果所有解码都失败，保持原始文件名
            }
          }
        }
      }

      // 创建下载URL
      const url = window.URL.createObjectURL(new Blob([data]))

      return {
        url,
        filename,
        size: data.size || 0,
        mimeType: headers.get
          ? headers.get('content-type') || 'application/octet-stream'
          : headers['content-type'] ||
            headers['Content-Type'] ||
            'application/octet-stream',
      }
    })
    .catch((error) => {
      console.error('文件下载API错误:', error)
      throw new Error(`文件下载失败: ${error.message || '未知错误'}`)
    })
}

/**
 * 更新文件信息
 * @param {number} id 文件ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export const update = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除文件（软删除）
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const destroy = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE',
  })
}

/**
 * 移动文件到目录
 * @param {number} id 文件ID
 * @param {Object} data 移动数据
 * @returns {Promise}
 */
export const move = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/move`,
    method: 'PUT',
    data,
  })
}

/**
 * 复制文件
 * @param {number} id 文件ID
 * @param {Object} data 复制数据
 * @returns {Promise}
 */
export const copy = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/copy`,
    method: 'POST',
    data,
  })
}

/**
 * 获取文件预览URL
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const getPreviewUrl = (id) => {
  return request({
    url: `${BASE_URL}/${id}/preview-url`,
    method: 'GET',
  })
}

/**
 * 预览文件内容
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const preview = (id) => {
  return request({
    url: `${BASE_URL}/${id}/preview`,
    method: 'GET',
  })
}

/**
 * 批量删除文件
 * @param {Array} fileIds 文件ID数组
 * @returns {Promise}
 */
export const batchDelete = (fileIds) => {
  return request({
    url: `${BASE_URL}/batch/delete`,
    method: 'POST',
    data: {
      file_ids: fileIds,
    },
  })
}

/**
 * 批量移动文件
 * @param {Array} fileIds 文件ID数组
 * @param {number} directoryId 目标目录ID
 * @returns {Promise}
 */
export const batchMove = (fileIds, directoryId) => {
  return request({
    url: `${BASE_URL}/batch/move`,
    method: 'POST',
    data: {
      file_ids: fileIds,
      directory_id: directoryId,
    },
  })
}

/**
 * 全局搜索文件和目录
 * @param {Object} params 搜索参数
 * @returns {Promise}
 */
export const search = (params = {}) => {
  return request({
    url: `${BASE_URL}/search`,
    method: 'POST',
    data: params,
  })
}
