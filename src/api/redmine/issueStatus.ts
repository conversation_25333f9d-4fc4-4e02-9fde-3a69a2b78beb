import request from '@/utils/request'

/**
 * 获取状态列表
 * @param params 查询参数
 */
export function getList(params: any) {
  return request({
    url: '/project/issue_statuses/getList',
    method: 'get',
    params,
  })
}

/**
 * 获取所有状态列表
 * @param params 查询参数
 */
export function getAllList(params: any) {
  return request({
    url: '/project/issue_statuses/getAllList',
    method: 'get',
    params,
  })
}

/**
 * 获取状态详情
 * @param params 查询参数
 */
export function getOverView(params: any) {
  return request({
    url: '/project/issue_statuses/overView',
    method: 'get',
    params,
  })
}

/**
 * 新增/编辑状态
 * @param params 状态数据
 */
export function doEdit(params: any) {
  return request({
    url: '/project/issue_statuses/doEdit',
    method: 'post',
    data: params,
  })
}

/**
 * 删除状态
 * @param params 删除参数
 */
export function doDelete(params: any) {
  return request({
    url: '/project/issue_statuses/doDelete',
    method: 'post',
    data: params,
  })
}
