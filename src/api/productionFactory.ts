import request from '@/utils/request'

/**
 *工厂列表
 */
export function getList(params: any) {
  return request({
    url: '/production/factory/getList',
    method: 'get',
    params,
  })
}

/**
 *工厂详情
 */
export function overView(data: any) {
  return request({
    url: '/production/factory/overView',
    method: 'post',
    data,
  })
}

/**
 *新增编辑工厂
 */
export function doEdit(data: any) {
  return request({
    url: '/production/factory/doEdit',
    method: 'post',
    data,
  })
}

/**
 *工厂删除
 */
export function doDelete(data: any) {
  return request({
    url: '/production/factory/doDelete',
    method: 'post',
    data,
  })
}
