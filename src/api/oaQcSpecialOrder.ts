/*
 * @Description: 特采申请单API
 * @Version: 1.0
 * @Autor: System
 * @Date: 2025-01-25 16:42:00
 * @LastEditors: System
 * @LastEditTime: 2025-01-25 16:42:00
 */
import request from '@/utils/request'

// 获取特采申请单列表
export function getList(params: any) {
  return request({
    url: '/tchip_oa/oa_qc_special_order/getList',
    method: 'get',
    params,
  })
}

// 获取特采申请单详情
export function getDetail(params: any) {
  return request({
    url: '/tchip_oa/oa_qc_special_order/show',
    method: 'get',
    params,
  })
}

// 审批特采申请单
export function approve(data: any) {
  return request({
    url: '/tchip_oa/oa_qc_special_order/approve',
    method: 'post',
    data, // { id, action: 'approve'|'reject', comment, reject_to_step }
  })
}

// 重新提交被退回的申请单
export function resubmit(data: any) {
  return request({
    url: '/tchip_oa/oa_qc_special_order/resubmit',
    method: 'post',
    data, // { id, special_reason, improvement_measures, expected_completion_date }
  })
}

// 更新特采订单信息（仅品质经理可操作）
export function updateOrderInfo(data: any) {
  return request({
    url: '/tchip_oa/oa_qc_special_order/update',
    method: 'post',
    data, // { id, special_details, special_reason, ... }
  })
}

// 取消特采申请单
export function cancel(data: any) {
  return request({
    url: '/tchip_oa/oa_qc_special_order/cancel',
    method: 'post',
    data,
  })
}

// 获取我的待审批列表
export function getMyApprovalList(params: any) {
  return request({
    url: '/tchip_oa/oa_qc_special_order/getMyApprovalList',
    method: 'get',
    params,
  })
}

// 获取审批历史
export function getApprovalHistory(id: number) {
  return request({
    url: `/tchip_oa/oa_qc_special_order/getApprovalHistory/${id}`,
    method: 'get',
  })
}

// 删除特采申请单
export function doDelete(data: any) {
  return request({
    url: '/tchip_oa/oa_qc_special_order/doDelete',
    method: 'post',
    data,
  })
}

// 检查用户是否可以审批
export function canUserApprove(id: number) {
  return request({
    url: `/tchip_oa/oa_qc_special_order/canApprove/${id}`,
    method: 'get',
  })
}

// 检查QC记录是否已有特采订单
export function checkSpecialOrderExists(params: any) {
  return request({
    url: '/tchip_oa/oa_qc_special_order/checkExists',
    method: 'get',
    params,
  })
}
