import request from '@/utils/request'

// API基础路径
const baseUrl = '/api/testfile'

// 文件管理接口
export function getFileList(params: any) {
  return request({
    url: `${baseUrl}/files`,
    method: 'get',
    params,
  })
}

export function getFileDetail(id: any) {
  return request({
    url: `${baseUrl}/files/${id}`,
    method: 'get',
  })
}

export function deleteFile(id: any) {
  return request({
    url: `${baseUrl}/files/${id}`,
    method: 'delete',
  })
}

export function downloadFile(id: any) {
  return request({
    url: `${baseUrl}/files/${id}/download`,
    method: 'get',
    responseType: 'blob',
  })
}

// 获取文件内容（用于预览）
export function getFileContent(id: any) {
  return request({
    url: `${baseUrl}/files/${id}/content`,
    method: 'get',
  })
}

export function getTreeData(params: any) {
  return request({
    url: `${baseUrl}/tree`,
    method: 'get',
    params,
  })
}

// 获取完整树结构（一次性加载所有层级）
export function getFullTreeData(params: any) {
  return request({
    url: `${baseUrl}/tree/full`,
    method: 'get',
    params,
  })
}

// 目录重排接口
export function createReorganizeTask(data: any) {
  return request({
    url: `${baseUrl}/reorganize`,
    method: 'post',
    data,
  })
}

export function getTaskList(params: any) {
  return request({
    url: `${baseUrl}/reorganize/tasks`,
    method: 'get',
    params,
  })
}

export function getTaskDetail(taskId: any) {
  return request({
    url: `${baseUrl}/reorganize/tasks/${taskId}`,
    method: 'get',
  })
}

// 产品管理接口
export function getProducts(params: any) {
  return request({
    url: `${baseUrl}/products`,
    method: 'get',
    params,
  })
}

export function createProduct(data: any) {
  return request({
    url: `${baseUrl}/products`,
    method: 'post',
    data,
  })
}

export function deleteProduct(id: any) {
  return request({
    url: `${baseUrl}/products/${id}`,
    method: 'delete',
  })
}

// 统计分析接口
export function getStatistics(params: any) {
  return request({
    url: `${baseUrl}/statistics`,
    method: 'get',
    params,
  })
}

// 老化测试统计接口
export function getAgingTestStatistics(params: any) {
  return request({
    url: `${baseUrl}/statistics/aging-test`,
    method: 'get',
    params,
  })
}

// 厂测统计接口
export function getFactoryTestStatistics(params: any) {
  return request({
    url: `${baseUrl}/statistics/factory-test`,
    method: 'get',
    params,
  })
}

// 工具函数
export function formatFileSize(bytes: any) {
  if (!bytes) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
