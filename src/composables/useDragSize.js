// src/composables/useDragSize.js
import { onMounted, onUnmounted } from 'vue'

/**
 * 让 el-drawer 支持拖拽改变宽/高 (直接控制抽屉宽度)
 * @param {Ref<Component|null>} drawerRef  通过 ref 拿到的 el-drawer 实例
 * @param {'rtl'|'ltr'|'ttb'|'btt'} direction  与 el-drawer 的 direction 保持一致
 */
export function useDragSize(drawerRef, direction = 'rtl') {
  let dragHandle = null
  let cleanup = null

  onMounted(() => {
    // 立即尝试查找已存在的 drawer
    tryFindAndInitDrawer()

    // 使用精确的 MutationObserver，只监听 el-overlay 的添加
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          const addedNodes = Array.from(mutation.addedNodes)
          for (const node of addedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 只关注 el-overlay 的添加（el-drawer 的容器）
              if (node.classList?.contains('el-overlay')) {
                // 使用 nextTick 确保 DOM 完全渲染
                setTimeout(() => {
                  const drawerInOverlay = node.querySelector('.el-drawer')
                  if (drawerInOverlay && !dragHandle) {
                    initSplitDrawer(drawerInOverlay)
                  }
                }, 10) // 减少延迟
              }
            }
          }
        }
      })
    })

    // 只监听 body 的直接子元素变化，减少监听范围
    observer.observe(document.body, {
      childList: true,
      subtree: false, // 不监听深层变化，提高性能
    })

    // 保存 observer 以便清理
    cleanup = () => {
      observer.disconnect()
      if (dragHandle && dragHandle.parentNode) {
        dragHandle.parentNode.removeChild(dragHandle)
        dragHandle = null
      }
    }
  })

  function tryFindAndInitDrawer() {
    let drawerContainer = null

    // 由于 el-drawer 使用了 teleport，实际 DOM 在 body 下，不在组件的 $el 中
    // 直接通过 class 查找 teleport 后的元素

    // 方式1: 查找最新的可见 el-drawer
    const allDrawers = document.querySelectorAll('.el-drawer')
    for (let i = allDrawers.length - 1; i >= 0; i--) {
      const d = allDrawers[i]
      if (d.style.display !== 'none' && d.offsetWidth > 0) {
        drawerContainer = d
        break
      }
    }

    // 方式2: 通过 overlay 查找
    if (!drawerContainer) {
      const overlay = document.querySelector('.el-overlay')
      if (overlay) {
        drawerContainer = overlay.querySelector('.el-drawer')
      }
    }

    if (drawerContainer && !dragHandle) {
      initSplitDrawer(drawerContainer)
    }
  }

  function initSplitDrawer(drawerContainer) {
    // 防止重复创建
    if (dragHandle) {
      return
    }

    // 检查是否已经有拖拽手柄
    if (drawerContainer.querySelector('.drag-resize-handle')) {
      return
    }

    try {
      // 创建拖拽手柄，直接控制 el-drawer 的宽度
      dragHandle = document.createElement('div')
      dragHandle.className = 'drag-resize-handle'
      dragHandle.style.cssText = `
        position: absolute;
        left: 2px;
        top: 0;
        bottom: 0;
        width: 10px;
        cursor: col-resize;
        z-index: 1000;
        background: transparent;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      `

      // 创建 iPhone 风格的横线指示器
      const indicator = document.createElement('div')
      indicator.style.cssText = `
        width: 4px;
        height: 40px;
        background: rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        pointer-events: none;
        opacity: 0;
        transform: scale(0.8);
      `
      dragHandle.appendChild(indicator)

      // 添加悬停效果
      dragHandle.addEventListener('mouseenter', () => {
        // 悬停时：显示并稍微变大
        indicator.style.opacity = '1'
        indicator.style.transform = 'scale(1)'
        indicator.style.height = '50px'
        indicator.style.width = '4px'
      })

      dragHandle.addEventListener('mouseleave', () => {
        // 离开时：隐藏指示器（除非正在拖拽）
        if (!isDragging) {
          indicator.style.opacity = '0'
          indicator.style.transform = 'scale(0.8)'
          indicator.style.height = '40px'
          indicator.style.width = '4px'
        }
      })

      // 拖拽逻辑
      let isDragging = false
      let startX = 0
      let startWidth = 0
      let animationId = null

      const onMouseDown = (e) => {
        isDragging = true
        startX = e.clientX
        startWidth = drawerContainer.offsetWidth

        // 拖拽时：确保显示并放大
        indicator.style.opacity = '1'
        indicator.style.height = '70px'
        indicator.style.width = '5px'
        indicator.style.transform = 'scale(1.2)'

        // 禁用文本选择和过渡效果
        document.body.style.userSelect = 'none'
        document.body.style.cursor = 'col-resize'
        drawerContainer.style.transition = 'none'

        document.addEventListener('mousemove', onMouseMove)
        document.addEventListener('mouseup', onMouseUp)

        e.preventDefault()
      }

      const onMouseMove = (e) => {
        if (!isDragging) return

        // 取消之前的动画帧
        if (animationId) {
          cancelAnimationFrame(animationId)
        }

        // 使用 requestAnimationFrame 优化性能
        animationId = requestAnimationFrame(() => {
          const deltaX = e.clientX - startX
          let newWidth = startWidth - deltaX // RTL方向：向右拖拽减小宽度

          // 限制最小和最大宽度
          const minWidth = 300
          const maxWidth = window.innerWidth * 0.9
          newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth))

          // 直接修改 el-drawer 的宽度
          drawerContainer.style.width = newWidth + 'px'

          animationId = null
        })
      }

      const onMouseUp = (e) => {
        if (!isDragging) return

        isDragging = false

        // 取消未完成的动画帧
        if (animationId) {
          cancelAnimationFrame(animationId)
          animationId = null
        }

        // 拖拽结束后，检查鼠标是否还在手柄上
        // 如果还在上面，保持悬停状态；如果不在，隐藏指示器
        const rect = dragHandle.getBoundingClientRect()
        const isMouseOver =
          e.clientX >= rect.left &&
          e.clientX <= rect.right &&
          e.clientY >= rect.top &&
          e.clientY <= rect.bottom

        if (isMouseOver) {
          // 鼠标还在上面，回到悬停状态
          indicator.style.height = '50px'
          indicator.style.width = '4px'
          indicator.style.transform = 'scale(1)'
        } else {
          // 鼠标不在上面，隐藏指示器
          indicator.style.opacity = '0'
          indicator.style.transform = 'scale(0.8)'
          indicator.style.height = '40px'
          indicator.style.width = '4px'
        }

        // 恢复样式
        document.body.style.userSelect = ''
        document.body.style.cursor = ''
        drawerContainer.style.transition = ''

        // // 保存当前宽度到 localStorage
        // const currentWidth = drawerContainer.offsetWidth
        // const storageKey = `useDragSize_drawer_width_${direction}`
        // localStorage.setItem(storageKey, currentWidth.toString())

        document.removeEventListener('mousemove', onMouseMove)
        document.removeEventListener('mouseup', onMouseUp)
      }

      dragHandle.addEventListener('mousedown', onMouseDown)

      // 将拖拽手柄添加到抽屉容器
      drawerContainer.appendChild(dragHandle)

      // // 恢复保存的宽度 - 延迟执行确保 DOM 完全渲染
      // const storageKey = `useDragSize_drawer_width_${direction}`
      // const savedWidth = localStorage.getItem(storageKey)
      // if (savedWidth) {
      //   const width = parseInt(savedWidth)
      //   if (width >= 300 && width <= window.innerWidth * 0.9) {
      //     // 使用 setTimeout 确保在 el-drawer 完全渲染后设置宽度
      //     setTimeout(() => {
      //       drawerContainer.style.width = width + 'px !important'
      //       drawerContainer.style.setProperty(
      //         'width',
      //         width + 'px',
      //         'important'
      //       )
      //     }, 50)
      //   }
      // }

      // 更新清理函数
      cleanup = () => {
        if (dragHandle) {
          dragHandle.removeEventListener('mousedown', onMouseDown)
          document.removeEventListener('mousemove', onMouseMove)
          document.removeEventListener('mouseup', onMouseUp)
          if (dragHandle.parentNode) {
            dragHandle.parentNode.removeChild(dragHandle)
          }
          dragHandle = null
        }
        if (animationId) {
          cancelAnimationFrame(animationId)
          animationId = null
        }
      }
    } catch (error) {
      console.error('useDragSize: 拖拽手柄初始化失败:', error)
    }
  }

  // 清理
  onUnmounted(() => {
    if (cleanup) {
      cleanup()
    }
  })

  // 手动初始化函数 - 推荐在 el-drawer 的 @opened 事件中调用
  const manualInit = () => {
    tryFindAndInitDrawer()

    // // 额外的宽度恢复机制
    // setTimeout(() => {
    //   const storageKey = `useDragSize_drawer_width_${direction}`
    //   const savedWidth = localStorage.getItem(storageKey)
    //   if (savedWidth) {
    //     const width = parseInt(savedWidth)
    //     if (width >= 300 && width <= window.innerWidth * 0.9) {
    //       const allDrawers = document.querySelectorAll('.el-drawer')
    //       for (let i = allDrawers.length - 1; i >= 0; i--) {
    //         const d = allDrawers[i]
    //         if (d.style.display !== 'none' && d.offsetWidth > 0) {
    //           d.style.setProperty('width', width + 'px', 'important')
    //           break
    //         }
    //       }
    //     }
    //   }
    // }, 100)
  }

  // 返回一些有用的方法供外部调用
  return {
    // 手动初始化（推荐使用）
    init: manualInit,

    // 销毁实例
    destroy: () => {
      if (cleanup) {
        cleanup()
      }
    },

    // 获取当前拖拽手柄
    getHandle: () => dragHandle,

    // 检查是否已初始化
    isInitialized: () => !!dragHandle,

    // 重置为默认宽度
    reset: () => {
      const allDrawers = document.querySelectorAll('.el-drawer')
      for (let i = allDrawers.length - 1; i >= 0; i--) {
        const d = allDrawers[i]
        if (d.style.display !== 'none' && d.offsetWidth > 0) {
          d.style.width = '69%' // 重置为默认宽度
          // const storageKey = `useDragSize_drawer_width_${direction}`
          // localStorage.removeItem(storageKey)
          break
        }
      }
    },

    // 清理旧的存储数据
    clearOldStorage: () => {
      // 清理可能存在的旧数据
      localStorage.removeItem('product_drawer_split_sizes')
      localStorage.removeItem(`useDragSize_split_sizes_${direction}`)
    },
  }
}
